
import { useState, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';

export const usePullToRefresh = () => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const queryClient = useQueryClient();

  const refresh = useCallback(async () => {
    setIsRefreshing(true);
    try {
      await queryClient.invalidateQueries();
      // Add a small delay for better UX
      await new Promise(resolve => setTimeout(resolve, 1000));
    } finally {
      setIsRefreshing(false);
    }
  }, [queryClient]);

  return {
    isRefreshing,
    refresh,
  };
};
