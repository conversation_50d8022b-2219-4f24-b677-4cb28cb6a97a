import { Package, ShoppingCart, Plus, Users, Settings } from "lucide-react";
import { useAppContext } from "@/contexts/AppContext";
import { AddItemSelectionModal } from "@/components/AddItemSelectionModal";
import { useState } from "react";
const TABS = [{
  value: "pantry",
  label: "<PERSON><PERSON><PERSON>",
  icon: Package
}, {
  value: "shopping",
  label: "Compras",
  icon: ShoppingCart
}, {
  value: "add",
  label: "",
  icon: Plus
}, {
  value: "family",
  label: "Família",
  icon: Users
}, {
  value: "settings",
  label: "Configurações",
  icon: Settings
}];
export function MobileNavigation() {
  const [showSelectionModal, setShowSelectionModal] = useState(false);
  const {
    activeTab,
    setActiveTab,
    setShowAddShoppingForm,
    setShowAddPantryForm
  } = useAppContext();
  return <>
      <nav className="fixed inset-x-0 bottom-0 z-50 block md:hidden">
      <div className="w-full bg-gradient-to-t from-white/95 via-white/90 to-white/80 backdrop-blur-xl shadow-[0_-4px_32px_0_rgba(0,0,0,0.08)] border-t border-green-100 rounded-t-3xl px-3 pt-2 pb-5 mx-0">
        <ul className="flex justify-between items-end pt-2 pb-5">
          {TABS.map(tab => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.value;
            const isAddButton = tab.value === "add";
            return <li key={tab.value} className={isAddButton ? "relative flex-1 flex flex-col items-center z-10" : "flex-1 flex flex-col items-center"}>
                <button aria-label={tab.label} onClick={() => {
                if (isAddButton) {
                  setShowSelectionModal(true);
                } else {
                  setActiveTab(tab.value);
                }
              }} className={isAddButton ? `
                        transition-all duration-300 flex flex-col items-center justify-center 
                        mt-[-24px] mb-0 rounded-full shadow-lg active:scale-95
                        ${isActive ? "bg-green-600 text-white scale-105 shadow-green-400/50" : "bg-green-500 text-white hover:bg-green-600 shadow-green-300/40"}
                        w-14 h-14 border-3 border-white` : `transition-all duration-200 flex flex-col items-center justify-center
                        py-2 px-1 rounded-xl active:scale-95 relative
                        ${isActive ? "bg-green-50 text-green-700 shadow-sm" : "text-gray-400 hover:bg-green-50/50 hover:text-green-600"}
                        w-full max-w-[64px] min-h-[52px]`} style={isAddButton ? {
                boxShadow: "0 4px 16px 2px #22c55e25"
              } : {}}>
                  <div className="relative">
                    <Icon size={isAddButton ? 28 : 20} strokeWidth={2.2} />
                  </div>
                  <span className={isAddButton ? "text-xs mt-1 font-semibold" : `text-[10px] mt-1 font-medium leading-tight text-center ${isActive ? "text-green-700" : "text-gray-500"}`}>
                    {tab.label}
                  </span>
                </button>
              </li>;
          })}
        </ul>
      </div>
    </nav>

    <AddItemSelectionModal isOpen={showSelectionModal} onClose={() => setShowSelectionModal(false)} onSelectList={() => {
      setShowSelectionModal(false);
      setActiveTab("shopping");
      setShowAddShoppingForm(true);
    }} onSelectPantry={() => {
      setShowSelectionModal(false);
      setActiveTab("pantry");
      setShowAddPantryForm(true);
    }} />
      </>;
}