-- Fix infinite recursion in family_members policies
-- Drop the problematic policy
DROP POLICY IF EXISTS "Family members can view other members" ON public.family_members;

-- Create a simpler policy that doesn't cause recursion
CREATE POLICY "Family members can view other members" ON public.family_members
FOR SELECT USING (
  user_id = auth.uid() OR
  family_group_id IN (
    SELECT family_group_id FROM public.family_members 
    WHERE user_id = auth.uid()
  )
);

-- Also fix the family_groups policy to avoid potential recursion
DROP POLICY IF EXISTS "Users can view their family groups" ON public.family_groups;

CREATE POLICY "Users can view their family groups" ON public.family_groups
FOR SELECT USING (
  created_by = auth.uid() OR 
  id IN (
    SELECT family_group_id FROM public.family_members 
    WHERE user_id = auth.uid()
  )
);

-- Fix pantry_items policies to avoid recursion
DROP POLICY IF EXISTS "Family members can view pantry items" ON public.pantry_items;
DROP POLICY IF EXISTS "Family members can update pantry items" ON public.pantry_items;
DROP POLICY IF EXISTS "Family members can delete pantry items" ON public.pantry_items;

CREATE POLICY "Family members can view pantry items" ON public.pantry_items
FOR SELECT USING (
  user_id = auth.uid() OR
  user_id IN (
    SELECT fm2.user_id FROM public.family_members fm1
    JOIN public.family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

CREATE POLICY "Family members can update pantry items" ON public.pantry_items
FOR UPDATE USING (
  user_id = auth.uid() OR
  user_id IN (
    SELECT fm2.user_id FROM public.family_members fm1
    JOIN public.family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

CREATE POLICY "Family members can delete pantry items" ON public.pantry_items
FOR DELETE USING (
  user_id = auth.uid() OR
  user_id IN (
    SELECT fm2.user_id FROM public.family_members fm1
    JOIN public.family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

-- Fix shopping_items policies to avoid recursion
DROP POLICY IF EXISTS "Family members can view shopping items" ON public.shopping_items;
DROP POLICY IF EXISTS "Family members can update shopping items" ON public.shopping_items;
DROP POLICY IF EXISTS "Family members can delete shopping items" ON public.shopping_items;

CREATE POLICY "Family members can view shopping items" ON public.shopping_items
FOR SELECT USING (
  user_id = auth.uid() OR 
  user_id IN (
    SELECT fm2.user_id FROM public.family_members fm1
    JOIN public.family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

CREATE POLICY "Family members can update shopping items" ON public.shopping_items
FOR UPDATE USING (
  user_id = auth.uid() OR 
  user_id IN (
    SELECT fm2.user_id FROM public.family_members fm1
    JOIN public.family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

CREATE POLICY "Family members can delete shopping items" ON public.shopping_items
FOR DELETE USING (
  user_id = auth.uid() OR 
  user_id IN (
    SELECT fm2.user_id FROM public.family_members fm1
    JOIN public.family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

-- Fix family_invitations policies
DROP POLICY IF EXISTS "Users can view invitations for their groups" ON public.family_invitations;

CREATE POLICY "Users can view invitations for their groups" ON public.family_invitations
FOR SELECT USING (
  invited_by = auth.uid() OR 
  family_group_id IN (
    SELECT family_group_id FROM public.family_members 
    WHERE user_id = auth.uid()
  )
);

-- Fix admin policies to avoid recursion
DROP POLICY IF EXISTS "Admins can update family groups" ON public.family_groups;
DROP POLICY IF EXISTS "Admins can manage family members" ON public.family_members;
DROP POLICY IF EXISTS "Admins can create invitations" ON public.family_invitations;
DROP POLICY IF EXISTS "Admins can update invitations" ON public.family_invitations;

CREATE POLICY "Admins can update family groups" ON public.family_groups
FOR UPDATE USING (
  id IN (
    SELECT family_group_id FROM public.family_members 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

CREATE POLICY "Admins can manage family members" ON public.family_members
FOR UPDATE USING (
  family_group_id IN (
    SELECT family_group_id FROM public.family_members 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

CREATE POLICY "Admins can create invitations" ON public.family_invitations
FOR INSERT WITH CHECK (
  family_group_id IN (
    SELECT family_group_id FROM public.family_members 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);

CREATE POLICY "Admins can update invitations" ON public.family_invitations
FOR UPDATE USING (
  family_group_id IN (
    SELECT family_group_id FROM public.family_members 
    WHERE user_id = auth.uid() AND role = 'admin'
  )
);
