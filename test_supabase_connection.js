// Test script to verify Supabase connection and data access
// Run this in browser console when logged in to test data access

console.log('Testing Supabase connection...');

// Test 1: Check if user is authenticated
const checkAuth = async () => {
  const { data: { user }, error } = await supabase.auth.getUser();
  console.log('Auth check:', { user: user?.id, error });
  return user;
};

// Test 2: Test pantry items query
const testPantryItems = async (user) => {
  if (!user) return;
  
  console.log('Testing pantry items query...');
  const { data, error } = await supabase
    .from('pantry_items')
    .select('*')
    .limit(5);
  
  console.log('Pantry items result:', { count: data?.length, error });
  if (error) console.error('Pantry error details:', error);
  return data;
};

// Test 3: Test shopping items query
const testShoppingItems = async (user) => {
  if (!user) return;
  
  console.log('Testing shopping items query...');
  const { data, error } = await supabase
    .from('shopping_items')
    .select('*')
    .limit(5);
  
  console.log('Shopping items result:', { count: data?.length, error });
  if (error) console.error('Shopping error details:', error);
  return data;
};

// Test 4: Test profiles query
const testProfiles = async (user) => {
  if (!user) return;
  
  console.log('Testing profiles query...');
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id);
  
  console.log('Profiles result:', { count: data?.length, error });
  if (error) console.error('Profiles error details:', error);
  return data;
};

// Test 5: Test family groups query
const testFamilyGroups = async (user) => {
  if (!user) return;
  
  console.log('Testing family groups query...');
  const { data, error } = await supabase
    .from('family_groups')
    .select('*');
  
  console.log('Family groups result:', { count: data?.length, error });
  if (error) console.error('Family groups error details:', error);
  return data;
};

// Test 6: Test family members query
const testFamilyMembers = async (user) => {
  if (!user) return;
  
  console.log('Testing family members query...');
  const { data, error } = await supabase
    .from('family_members')
    .select('*');
  
  console.log('Family members result:', { count: data?.length, error });
  if (error) console.error('Family members error details:', error);
  return data;
};

// Run all tests
const runAllTests = async () => {
  try {
    const user = await checkAuth();
    
    if (!user) {
      console.error('User not authenticated!');
      return;
    }
    
    await testProfiles(user);
    await testPantryItems(user);
    await testShoppingItems(user);
    await testFamilyGroups(user);
    await testFamilyMembers(user);
    
    console.log('All tests completed!');
  } catch (error) {
    console.error('Test failed:', error);
  }
};

// Auto-run tests
runAllTests();

// Export for manual testing
window.testSupabase = {
  checkAuth,
  testPantryItems,
  testShoppingItems,
  testProfiles,
  testFamilyGroups,
  testFamilyMembers,
  runAllTests
};
