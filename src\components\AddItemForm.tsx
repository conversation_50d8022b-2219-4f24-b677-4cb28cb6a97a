import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus } from "lucide-react";
import { PantryItem } from "@/types";
import { useToast } from "@/hooks/use-toast";

interface AddItemFormProps {
  onAddItem: (item: Omit<PantryItem, 'id' | 'addedDate' | 'addedById'>) => void;
  showCard?: boolean;
}

const categories = [
  "Laticínios",
  "Carnes",
  "Verduras",
  "Frutas",
  "Grãos",
  "Enlatados",
  "Bebidas",
  "Temperos",
  "Congelados",
  "Limpeza",
  "Outros"
];

const units = [
  "unidades",
  "kg",
  "gramas",
  "litros",
  "ml",
  "pacotes",
  "caixas",
  "latas"
];

export const AddItemForm = ({ onAddItem, showCard = true }: AddItemFormProps) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: "",
    quantity: "",
    unit: "",
    category: "",
    expiryDate: "",
    isLowStock: false
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    console.log('AddItemForm - Form data:', formData);

    if (!formData.name || !formData.quantity || !formData.unit || !formData.category || !formData.expiryDate) {
      console.log('AddItemForm - Validation failed:', {
        name: !formData.name,
        quantity: !formData.quantity,
        unit: !formData.unit,
        category: !formData.category,
        expiryDate: !formData.expiryDate
      });
      toast({
        title: "Erro",
        description: "Por favor, preencha todos os campos obrigatórios.",
        variant: "destructive"
      });
      return;
    }

    const itemToAdd = {
      name: formData.name,
      quantity: parseFloat(formData.quantity),
      unit: formData.unit,
      category: formData.category,
      expiryDate: formData.expiryDate,
      isLowStock: formData.isLowStock
    };

    console.log('AddItemForm - Adding item:', itemToAdd);

    try {
      onAddItem(itemToAdd);

      // Reset form
      setFormData({
        name: "",
        quantity: "",
        unit: "",
        category: "",
        expiryDate: "",
        isLowStock: false
      });

      toast({
        title: "Sucesso!",
        description: "Item adicionado à despensa com sucesso.",
      });
    } catch (error) {
      console.error('AddItemForm - Error adding item:', error);
      toast({
        title: "Erro",
        description: "Erro ao adicionar item. Tente novamente.",
        variant: "destructive"
      });
    }
  };

  const formContent = (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Nome do Item *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            placeholder="Ex: Leite, Arroz, Tomate..."
            className="bg-white"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="category">Categoria *</Label>
          <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
            <SelectTrigger className="bg-white">
              <SelectValue placeholder="Selecione a categoria" />
            </SelectTrigger>
            <SelectContent className="bg-white">
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="quantity">Quantidade *</Label>
          <Input
            id="quantity"
            type="number"
            value={formData.quantity}
            onChange={(e) => setFormData(prev => ({ ...prev, quantity: e.target.value }))}
            placeholder="Ex: 2"
            min="0.1"
            step="0.1"
            className="bg-white"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="unit">Unidade *</Label>
          <Select value={formData.unit} onValueChange={(value) => setFormData(prev => ({ ...prev, unit: value }))}>
            <SelectTrigger className="bg-white">
              <SelectValue placeholder="Selecione a unidade" />
            </SelectTrigger>
            <SelectContent className="bg-white">
              {units.map((unit) => (
                <SelectItem key={unit} value={unit}>
                  {unit}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="expiryDate">Data de Validade *</Label>
          <Input
            id="expiryDate"
            type="date"
            value={formData.expiryDate}
            onChange={(e) => setFormData(prev => ({ ...prev, expiryDate: e.target.value }))}
            className="bg-white"
          />
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="isLowStock"
            checked={formData.isLowStock}
            onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isLowStock: checked }))}
          />
          <Label htmlFor="isLowStock">Marcar como estoque baixo</Label>
        </div>
      </div>

      <Button type="submit" className="w-full bg-green-600 hover:bg-green-700 text-white">
        <Plus className="w-4 h-4 mr-2" />
        Adicionar à Despensa
      </Button>
    </form>
  );

  if (!showCard) {
    return formContent;
  }

  return (
    <Card className="bg-white shadow-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Plus className="w-5 h-5 text-green-600" />
          Adicionar Item à Despensa
        </CardTitle>
      </CardHeader>
      <CardContent>
        {formContent}
      </CardContent>
    </Card>
  );
};
