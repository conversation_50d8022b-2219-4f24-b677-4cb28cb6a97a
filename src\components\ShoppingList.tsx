import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Trash2, ShoppingCart, Save, Sparkles } from "lucide-react";
import { ShoppingItem } from "@/types";
import { AddShoppingItemForm } from "./AddShoppingItemForm";
import { SavePurchasesModal } from "./SavePurchasesModal";
import { AdvancedShoppingModal } from "./AdvancedShoppingModal";
import { useState } from "react";
import { useAppContext } from "@/contexts/AppContext";

interface ShoppingListProps {
  items: ShoppingItem[];
  onToggleItem: (id: string) => Promise<void>;
  onDeleteItem: (id: string) => Promise<void>;
  onAddItem: (item: Omit<ShoppingItem, 'id' | 'addedBy'>) => void;
  onAddItems: (items: Omit<ShoppingItem, 'id' | 'addedBy'>[]) => Promise<void>;
  onSaveToPantry: (items: any[]) => Promise<void>;
  onClearCompleted: () => Promise<void>;
}

export const ShoppingList = ({
  items,
  onToggleItem,
  onDeleteItem,
  onAddItem,
  onAddItems,
  onSaveToPantry,
  onClearCompleted
}: ShoppingListProps) => {
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [showAdvancedModal, setShowAdvancedModal] = useState(false);
  const { showAddShoppingForm, setShowAddShoppingForm, setActiveTab } = useAppContext();

  const pendingItems = items.filter(item => !item.completed);
  const completedItems = items.filter(item => item.completed);

  return (
    <div className="space-y-6">
      {/* Header with Add Button */}
      <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-2">
          <ShoppingCart className="w-6 h-6 text-green-600" />
          <h2 className="text-xl font-semibold text-gray-800">Lista de Compras</h2>
        </div>
        <div className="flex gap-2 flex-wrap">
          {completedItems.length > 0 && (
            <Button
              onClick={() => setShowSaveModal(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Save className="w-4 h-4 mr-2" />
              Salvar Compras
            </Button>
          )}

          <Button
            onClick={() => setShowAdvancedModal(true)}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            <Sparkles className="w-4 h-4 mr-2" />
            Lista Inteligente
          </Button>
        </div>
      </div>

      {/* Summary */}
      <div className="grid grid-cols-2 gap-2 sm:gap-4">
        <Card className="bg-white shadow-sm">
          <CardHeader className="pb-1 sm:pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium text-gray-600">Itens Pendentes</CardTitle>
          </CardHeader>
          <CardContent className="pt-1 sm:pt-3">
            <div className="text-xl sm:text-2xl font-bold text-blue-600">{pendingItems.length}</div>
          </CardContent>
        </Card>

        <Card className="bg-white shadow-sm">
          <CardHeader className="pb-1 sm:pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium text-gray-600">Itens Comprados</CardTitle>
          </CardHeader>
          <CardContent className="pt-1 sm:pt-3">
            <div className="text-xl sm:text-2xl font-bold text-green-600">{completedItems.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Pending Items */}
      {pendingItems.length > 0 && (
        <Card className="bg-white shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-800">Para Comprar</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 overflow-x-auto">
              {pendingItems.map((item) => (
                <div
                  key={item.id}
                  className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-100 flex-wrap sm:flex-nowrap cursor-pointer hover:bg-blue-100 transition-colors"
                  onClick={async () => {
                    try {
                      await onToggleItem(item.id);
                    } catch (error) {
                      console.error('Error toggling item:', error);
                    }
                  }}
                >
                  <div className="flex items-center gap-3 w-0 min-w-0 flex-1">
                    <Checkbox
                      checked={item.completed}
                      onCheckedChange={async () => {
                        try {
                          await onToggleItem(item.id);
                        } catch (error) {
                          console.error('Error toggling item:', error);
                        }
                      }}
                      onClick={(e) => e.stopPropagation()} // Prevent double toggle
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex flex-wrap items-center gap-2">
                        <h3 className="font-medium text-gray-800 break-words">{item.name}</h3>
                        <Badge variant="outline" className="text-xs whitespace-nowrap">
                          {item.category}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-600 truncate">
                        {item.quantity} {item.unit} • Adicionado por {item.addedBy}
                      </div>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={async (e) => {
                      e.stopPropagation(); // Prevent triggering the card click
                      try {
                        await onDeleteItem(item.id);
                      } catch (error) {
                        console.error('Error deleting item:', error);
                      }
                    }}
                    className="text-red-600 hover:text-red-700 shrink-0 ml-2 mt-2 sm:mt-0"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Completed Items */}
      {completedItems.length > 0 && (
        <Card className="bg-white shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-800">Comprados</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 overflow-x-auto">
              {completedItems.map((item) => (
                <div
                  key={item.id}
                  className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-100 flex-wrap sm:flex-nowrap cursor-pointer hover:bg-green-100 transition-colors"
                  onClick={async () => {
                    try {
                      await onToggleItem(item.id);
                    } catch (error) {
                      console.error('Error toggling item:', error);
                    }
                  }}
                >
                  <div className="flex items-center gap-3 w-0 min-w-0 flex-1">
                    <Checkbox
                      checked={item.completed}
                      onCheckedChange={async () => {
                        try {
                          await onToggleItem(item.id);
                        } catch (error) {
                          console.error('Error toggling item:', error);
                        }
                      }}
                      onClick={(e) => e.stopPropagation()} // Prevent double toggle
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex flex-wrap items-center gap-2">
                        <h3 className="font-medium text-gray-800 line-through break-words">{item.name}</h3>
                        <Badge variant="outline" className="text-xs whitespace-nowrap">
                          {item.category}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-600 truncate">
                        {item.quantity} {item.unit} • Comprado
                      </div>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={async (e) => {
                      e.stopPropagation(); // Prevent triggering the card click
                      try {
                        await onDeleteItem(item.id);
                      } catch (error) {
                        console.error('Error deleting item:', error);
                      }
                    }}
                    className="text-red-600 hover:text-red-700 shrink-0 ml-2 mt-2 sm:mt-0"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {items.length === 0 && (
        <Card className="bg-white shadow-sm">
          <CardContent className="text-center py-12">
            <ShoppingCart className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-600 mb-2">Sua lista está vazia</h3>
            <p className="text-gray-500 mb-4">Adicione itens à sua lista de compras para começar</p>
          </CardContent>
        </Card>
      )}

      <SavePurchasesModal
        isOpen={showSaveModal}
        onClose={() => setShowSaveModal(false)}
        completedItems={completedItems}
        onSaveToPantry={onSaveToPantry}
        onClearCompleted={onClearCompleted}
        onNavigateToPantry={() => setActiveTab("pantry")}
      />

      {showAddShoppingForm && (
        <AddShoppingItemForm
          isOpen={showAddShoppingForm}
          onClose={() => setShowAddShoppingForm(false)}
          onAddItem={onAddItem}
          onOpenAdvancedMode={() => setShowAdvancedModal(true)}
        />
      )}

      <AdvancedShoppingModal
        isOpen={showAdvancedModal}
        onClose={() => setShowAdvancedModal(false)}
      />
    </div>
  );
};
