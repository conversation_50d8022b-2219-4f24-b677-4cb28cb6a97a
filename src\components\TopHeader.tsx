
import { Bell, Share } from "lucide-react";
import { NotificationBadge } from "./NotificationBadge";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { ShareModal } from "./ShareModal";
import { NotificationDropdown } from "./NotificationDropdown";
import { NotificationModal } from "./NotificationModal";
import { useNotifications } from "@/hooks/useNotifications";
import { useIsMobile } from "@/hooks/use-mobile";
import { UserProfile } from "./UserProfile";

export const TopHeader = () => {
  const [showShareModal, setShowShareModal] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const {
    unreadCount
  } = useNotifications();
  const isMobile = useIsMobile();

  return (
    <>
      <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-md border-b border-green-100 shadow-sm">
        <div className="container mx-auto px-4 py-3 flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center gap-2">
            <img
              src="https://i.postimg.cc/mrFXt52R/logo-despensa-removebg.png"
              alt="PantryPal"
              className="w-8 h-8 rounded-lg object-contain"
            />
            <h1 className="text-xl font-bold text-green-700">DespesaA2</h1>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            {/* User Profile */}
            {!isMobile && <UserProfile />}
            
            {/* Notifications */}
            <div className="relative">
              <Button variant="ghost" size="icon" onClick={() => setShowNotifications(!showNotifications)} className="relative">
                <Bell className="w-5 h-5 text-gray-600" />
                {unreadCount > 0 && <NotificationBadge count={unreadCount} />}
              </Button>
              
              {/* Show modal on mobile, dropdown on desktop */}
              {isMobile ? (
                <NotificationModal isOpen={showNotifications} onClose={() => setShowNotifications(false)} />
              ) : (
                showNotifications && <NotificationDropdown onClose={() => setShowNotifications(false)} />
              )}
            </div>

            {/* Share */}
            <Button variant="ghost" size="icon" onClick={() => setShowShareModal(true)}>
              <Share className="w-5 h-5 text-gray-600" />
            </Button>
          </div>
        </div>
      </header>

      {/* Modals */}
      <ShareModal isOpen={showShareModal} onClose={() => setShowShareModal(false)} />
    </>
  );
};
