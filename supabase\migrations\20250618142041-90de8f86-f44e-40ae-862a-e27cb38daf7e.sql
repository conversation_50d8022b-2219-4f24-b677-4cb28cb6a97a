
-- Create the pantry_items table first
CREATE TABLE IF NOT EXISTS public.pantry_items (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  quantity integer NOT NULL,
  unit text NOT NULL,
  category text NOT NULL DEFAULT 'Outros',
  expiry_date date,
  is_low_stock boolean NOT NULL DEFAULT false,
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);

-- Create the shopping_items table
CREATE TABLE IF NOT EXISTS public.shopping_items (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  quantity integer NOT NULL,
  unit text NOT NULL,
  category text NOT NULL DEFAULT 'Outros',
  checked boolean NOT NULL DEFAULT false,
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);

-- Create user profiles table
CREATE TABLE IF NOT EXISTS public.profiles (
  id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email text,
  full_name text,
  avatar_url text,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);

-- Create family groups table
CREATE TABLE IF NOT EXISTS public.family_groups (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  invite_code text UNIQUE NOT NULL DEFAULT substring(md5(random()::text), 1, 8),
  created_by uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);

-- Create family members table
CREATE TABLE IF NOT EXISTS public.family_members (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  family_group_id uuid REFERENCES public.family_groups(id) ON DELETE CASCADE NOT NULL,
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  role text NOT NULL DEFAULT 'member' CHECK (role IN ('admin', 'member')),
  joined_at timestamp with time zone NOT NULL DEFAULT now(),
  UNIQUE(family_group_id, user_id)
);

-- Create family invitations table
CREATE TABLE IF NOT EXISTS public.family_invitations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  family_group_id uuid REFERENCES public.family_groups(id) ON DELETE CASCADE NOT NULL,
  invited_by uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  email text NOT NULL,
  status text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined')),
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  expires_at timestamp with time zone NOT NULL DEFAULT (now() + interval '7 days'),
  UNIQUE(family_group_id, email)
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS pantry_items_user_id_idx ON public.pantry_items(user_id);
CREATE INDEX IF NOT EXISTS pantry_items_expiry_date_idx ON public.pantry_items(expiry_date);
CREATE INDEX IF NOT EXISTS shopping_items_user_id_idx ON public.shopping_items(user_id);
CREATE INDEX IF NOT EXISTS family_members_user_id_idx ON public.family_members(user_id);
CREATE INDEX IF NOT EXISTS family_members_family_group_id_idx ON public.family_members(family_group_id);

-- Enable Row Level Security
ALTER TABLE public.pantry_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.shopping_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.family_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.family_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.family_invitations ENABLE ROW LEVEL SECURITY;

-- Create security definer function to get user's family groups
CREATE OR REPLACE FUNCTION public.get_user_family_groups(user_uuid uuid)
RETURNS TABLE(family_group_id uuid) AS $$
BEGIN
  RETURN QUERY
  SELECT fm.family_group_id
  FROM public.family_members fm
  WHERE fm.user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER STABLE;

-- RLS Policies for profiles
CREATE POLICY "Users can view own profile" ON public.profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- RLS Policies for family_groups
CREATE POLICY "Family members can view their groups" ON public.family_groups
  FOR SELECT USING (
    id IN (SELECT family_group_id FROM public.get_user_family_groups(auth.uid()))
  );

CREATE POLICY "Users can create family groups" ON public.family_groups
  FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Family admins can update groups" ON public.family_groups
  FOR UPDATE USING (
    id IN (
      SELECT fm.family_group_id 
      FROM public.family_members fm 
      WHERE fm.user_id = auth.uid() AND fm.role = 'admin'
    )
  );

-- RLS Policies for family_members
CREATE POLICY "Family members can view family members" ON public.family_members
  FOR SELECT USING (
    family_group_id IN (SELECT family_group_id FROM public.get_user_family_groups(auth.uid()))
  );

CREATE POLICY "Family admins can manage members" ON public.family_members
  FOR ALL USING (
    family_group_id IN (
      SELECT fm.family_group_id 
      FROM public.family_members fm 
      WHERE fm.user_id = auth.uid() AND fm.role = 'admin'
    )
  );

CREATE POLICY "Users can join families" ON public.family_members
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for family_invitations
CREATE POLICY "Family members can view invitations" ON public.family_invitations
  FOR SELECT USING (
    family_group_id IN (SELECT family_group_id FROM public.get_user_family_groups(auth.uid()))
  );

CREATE POLICY "Family admins can manage invitations" ON public.family_invitations
  FOR ALL USING (
    family_group_id IN (
      SELECT fm.family_group_id 
      FROM public.family_members fm 
      WHERE fm.user_id = auth.uid() AND fm.role = 'admin'
    )
  );

-- RLS Policies for pantry_items to support family sharing
CREATE POLICY "Users can view family pantry items" ON public.pantry_items
  FOR SELECT USING (
    user_id = auth.uid() OR 
    user_id IN (
      SELECT fm.user_id 
      FROM public.family_members fm 
      WHERE fm.family_group_id IN (SELECT family_group_id FROM public.get_user_family_groups(auth.uid()))
    )
  );

CREATE POLICY "Users can insert pantry items" ON public.pantry_items
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update family pantry items" ON public.pantry_items
  FOR UPDATE USING (
    user_id = auth.uid() OR 
    user_id IN (
      SELECT fm.user_id 
      FROM public.family_members fm 
      WHERE fm.family_group_id IN (SELECT family_group_id FROM public.get_user_family_groups(auth.uid()))
    )
  );

CREATE POLICY "Users can delete family pantry items" ON public.pantry_items
  FOR DELETE USING (
    user_id = auth.uid() OR 
    user_id IN (
      SELECT fm.user_id 
      FROM public.family_members fm 
      WHERE fm.family_group_id IN (SELECT family_group_id FROM public.get_user_family_groups(auth.uid()))
    )
  );

-- RLS Policies for shopping_items to support family sharing
CREATE POLICY "Users can view family shopping items" ON public.shopping_items
  FOR SELECT USING (
    user_id = auth.uid() OR 
    user_id IN (
      SELECT fm.user_id 
      FROM public.family_members fm 
      WHERE fm.family_group_id IN (SELECT family_group_id FROM public.get_user_family_groups(auth.uid()))
    )
  );

CREATE POLICY "Users can insert shopping items" ON public.shopping_items
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update family shopping items" ON public.shopping_items
  FOR UPDATE USING (
    user_id = auth.uid() OR 
    user_id IN (
      SELECT fm.user_id 
      FROM public.family_members fm 
      WHERE fm.family_group_id IN (SELECT family_group_id FROM public.get_user_family_groups(auth.uid()))
    )
  );

CREATE POLICY "Users can delete family shopping items" ON public.shopping_items
  FOR DELETE USING (
    user_id = auth.uid() OR 
    user_id IN (
      SELECT fm.user_id 
      FROM public.family_members fm 
      WHERE fm.family_group_id IN (SELECT family_group_id FROM public.get_user_family_groups(auth.uid()))
    )
  );

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_pantry_items_updated_at 
  BEFORE UPDATE ON public.pantry_items 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_shopping_items_updated_at 
  BEFORE UPDATE ON public.shopping_items 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_profiles_updated_at 
  BEFORE UPDATE ON public.profiles 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_family_groups_updated_at 
  BEFORE UPDATE ON public.family_groups 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to handle new user profile creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name, avatar_url)
  VALUES (
    NEW.id,
    NEW.email,
    NEW.raw_user_meta_data->>'full_name',
    NEW.raw_user_meta_data->>'avatar_url'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user profile creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
