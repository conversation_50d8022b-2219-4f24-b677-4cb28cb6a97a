import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ShoppingItem } from "@/types";
import { useToast } from "@/hooks/use-toast";
import { X, Sparkles } from "lucide-react";
import { getProductsByCategory } from "@/data/products";

interface AddShoppingItemFormProps {
  isOpen: boolean;
  onClose: () => void;
  onAddItem: (item: Omit<ShoppingItem, 'id' | 'addedBy'>) => void;
  onOpenAdvancedMode?: () => void;
}

const categories = [
  "Laticínios",
  "Carnes",
  "Verduras",
  "Frutas",
  "Grãos",
  "Enlatados",
  "Bebidas",
  "Temperos",
  "Congelados",
  "Limpeza",
  "Outros"
];

const units = [
  "unidades",
  "kg",
  "gramas",
  "litros",
  "ml",
  "pacotes",
  "caixas",
  "latas"
];

export const AddShoppingItemForm = ({ isOpen, onClose, onAddItem, onOpenAdvancedMode }: AddShoppingItemFormProps) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: "",
    quantity: "",
    unit: "",
    category: ""
  });
  const [availableProducts, setAvailableProducts] = useState<string[]>([]);
  const [showNewProductInput, setShowNewProductInput] = useState(false);

  useEffect(() => {
    if (formData.category) {
      const products = getProductsByCategory(formData.category);
      setAvailableProducts(products?.map(p => p.name) || []);
      setShowNewProductInput(false);
    } else {
      setAvailableProducts([]);
    }
  }, [formData.category]);

  const handleCategoryChange = (value: string) => {
    setFormData(prev => ({ ...prev, category: value, name: "" }));
  };

  const handleProductSelect = (value: string) => {
    setFormData(prev => ({ ...prev, name: value }));
  };

  const handleNewProductToggle = () => {
    setShowNewProductInput(!showNewProductInput);
    setFormData(prev => ({ ...prev, name: "" }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.quantity || !formData.unit || !formData.category) {
      toast({
        title: "Erro",
        description: "Por favor, preencha todos os campos obrigatórios.",
        variant: "destructive"
      });
      return;
    }

    const itemToAdd = {
      name: formData.name,
      quantity: parseFloat(formData.quantity),
      unit: formData.unit,
      category: formData.category,
      completed: false
    };

    try {
      onAddItem(itemToAdd);

      // Reset form
      setFormData({
        name: "",
        quantity: "",
        unit: "",
        category: ""
      });

      toast({
        title: "Sucesso!",
        description: "Item adicionado à lista de compras.",
      });

      // Close modal after successful submission
      onClose();
    } catch (error) {
      console.error('Error adding item:', error);
      toast({
        title: "Erro",
        description: "Erro ao adicionar item. Tente novamente.",
        variant: "destructive"
      });
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      onClick={(e) => {
        // Fechar modal ao clicar no backdrop
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div className="bg-white rounded-lg p-6 w-full max-w-md relative">
        {/* Botão de fechar no canto superior direito */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="absolute top-4 right-4 h-8 w-8 p-0 hover:bg-gray-100 z-10"
        >
          <X className="h-4 w-4" />
        </Button>

        <div className="pr-10 mb-4">
          <h2 className="text-xl font-bold">Adicionar Item à Lista</h2>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="category">Categoria *</Label>
              <Select
                value={formData.category}
                onValueChange={handleCategoryChange}
              >
                <SelectTrigger className="bg-white">
                  <SelectValue placeholder="Selecione a categoria" />
                </SelectTrigger>
                <SelectContent className="bg-white">
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="name">Nome do Item *</Label>
              {formData.category ? (
                <>
                  {!showNewProductInput ? (
                    <>
                      <Select
                        value={formData.name}
                        onValueChange={handleProductSelect}
                      >
                        <SelectTrigger className="bg-white">
                          <SelectValue placeholder="Selecione um produto" />
                        </SelectTrigger>
                        <SelectContent className="bg-white">
                          {availableProducts.map((product) => (
                            <SelectItem key={product} value={product}>
                              {product}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <Button
                        type="button"
                        variant="link"
                        onClick={handleNewProductToggle}
                        className="text-sm text-green-600 p-0 h-auto"
                      >
                        Não encontrou? Criar novo produto
                      </Button>
                    </>
                  ) : (
                    <div className="flex gap-2">
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Digite o nome do novo produto"
                        className="bg-white flex-1"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleNewProductToggle}
                        className="h-10"
                      >
                        Voltar
                      </Button>
                    </div>
                  )}
                </>
              ) : (
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Selecione a categoria primeiro"
                  className="bg-white"
                  disabled
                />
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="quantity">Quantidade *</Label>
              <Input
                id="quantity"
                type="number"
                value={formData.quantity}
                onChange={(e) => setFormData(prev => ({ ...prev, quantity: e.target.value }))}
                placeholder="Ex: 2"
                min="0.1"
                step="0.1"
                className="bg-white"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="unit">Unidade *</Label>
              <Select value={formData.unit} onValueChange={(value) => setFormData(prev => ({ ...prev, unit: value }))}>
                <SelectTrigger className="bg-white">
                  <SelectValue placeholder="Selecione a unidade" />
                </SelectTrigger>
                <SelectContent className="bg-white">
                  {units.map((unit) => (
                    <SelectItem key={unit} value={unit}>
                      {unit}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex flex-col gap-4">
            <Button type="submit" className="w-full bg-green-600 hover:bg-green-700 text-white">
              Adicionar à Lista
            </Button>

            {onOpenAdvancedMode && (
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  onClose();
                  onOpenAdvancedMode();
                }}
                className="w-full text-green-600 border-green-600 hover:bg-green-50"
              >
                <Sparkles className="w-4 h-4 mr-2" />
                Lista Inteligente
              </Button>
            )}

            <Button
              variant="outline"
              onClick={onClose}
              className="w-full"
            >
              Cancelar
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddShoppingItemForm;
