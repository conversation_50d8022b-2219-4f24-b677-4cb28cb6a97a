// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://mtdpixvwicizdfprrgzu.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im10ZHBpeHZ3aWNpemRmcHJyZ3p1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMyNzE3NjEsImV4cCI6MjA1ODg0Nzc2MX0.ThYt6MLanVbLcAWkJZcs_KgcwB91TdVwO1mtsJacaWE";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);