
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { FamilyGroup } from '@/types';
import { useToast } from './use-toast';

export const useFamilyGroups = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: familyGroups = [], isLoading, error } = useQuery({
    queryKey: ['familyGroups', user?.id],
    queryFn: async () => {
      if (!user) return [];

      try {
        // First, get the user's family memberships
        const { data: memberships, error: membershipError } = await supabase
          .from('family_members')
          .select('family_group_id')
          .eq('user_id', user.id);

        if (membershipError) {
          console.error('Error fetching memberships:', membershipError);
          return [];
        }

        if (!memberships || memberships.length === 0) {
          return [];
        }

        // Then get the family groups for those memberships
        const groupIds = memberships.map(m => m.family_group_id);
        const { data: groups, error: groupsError } = await supabase
          .from('family_groups')
          .select('*')
          .in('id', groupIds)
          .order('created_at', { ascending: false });

        if (groupsError) {
          console.error('Error fetching family groups:', groupsError);
          return [];
        }

        return groups as FamilyGroup[];
      } catch (error) {
        console.error('Error in family groups query:', error);
        return [];
      }
    },
    enabled: !!user,
  });

  const createGroupMutation = useMutation({
    mutationFn: async (groupName: string) => {
      if (!user) throw new Error('User not authenticated');

      try {
        // Generate a unique invite code
        const inviteCode = Math.random().toString(36).substring(2, 8).toUpperCase();

        // Create the family group
        const { data: group, error: groupError } = await supabase
          .from('family_groups')
          .insert({
            name: groupName,
            created_by: user.id,
            invite_code: inviteCode,
          })
          .select()
          .single();

        if (groupError) {
          console.error('Error creating group:', groupError);
          throw new Error('Erro ao criar grupo familiar');
        }

        // Add creator as admin member
        const { error: memberError } = await supabase
          .from('family_members')
          .insert({
            family_group_id: group.id,
            user_id: user.id,
            role: 'admin',
          });

        if (memberError) {
          console.error('Error adding member:', memberError);
          // Try to clean up the group if member creation fails
          await supabase.from('family_groups').delete().eq('id', group.id);
          throw new Error('Erro ao adicionar membro ao grupo');
        }

        return group;
      } catch (error) {
        console.error('Error in createGroup:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['familyGroups'] });
      toast({
        title: "Grupo criado",
        description: "Grupo familiar criado com sucesso.",
      });
    },
    onError: (error) => {
      toast({
        title: "Erro",
        description: "Erro ao criar grupo familiar.",
        variant: "destructive",
      });
      console.error('Error creating family group:', error);
    },
  });

  const joinGroupMutation = useMutation({
    mutationFn: async (inviteCode: string) => {
      if (!user) throw new Error('User not authenticated');
      
      // Validate and normalize invite code format (6 alphanumeric chars)
      if (!inviteCode) {
        throw new Error('Por favor, insira um código de convite');
      }
      
      // Normalize to uppercase and remove spaces/special chars
      const normalizedCode = inviteCode.toUpperCase().replace(/[^A-Z0-9]/g, '');
      
      if (normalizedCode.length !== 6) {
        throw new Error('O código deve ter exatamente 6 caracteres alfanuméricos (letras e números)');
      }
      
      // Use normalized code for the rest of the operation
      inviteCode = normalizedCode;

      // Find group by invite code with proper headers
      const { data: group, error: groupError } = await supabase
        .from('family_groups')
        .select('*')
        .eq('invite_code', inviteCode)
        .single();
        
      if (groupError) {
        console.error('Detailed group search error:', {
          code: groupError.code,
          message: groupError.message,
          details: groupError.details
        });
        
        if (groupError.code === 'PGRST116') {
          throw new Error('Código de convite inválido ou grupo não encontrado');
        }
        throw new Error('Erro ao buscar grupo. Tente novamente mais tarde.');
      }
      
      if (!group) {
        throw new Error('Nenhum grupo encontrado com este código de convite');
      }

      if (groupError || !group) {
        throw new Error('Grupo não encontrado. Verifique o código e tente novamente.');
      }

      // Check if user is already a member
      const { data: existingMember } = await supabase
        .from('family_members')
        .select('*')
        .eq('family_group_id', group.id)
        .eq('user_id', user.id)
        .single();

      if (existingMember) {
        throw new Error('Você já é membro deste grupo');
      }

      // Add user as member
      const { error: memberError } = await supabase
        .from('family_members')
        .insert({
          family_group_id: group.id,
          user_id: user.id,
          role: 'member',
        });

      if (memberError) throw memberError;

      return group;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['familyGroups'] });
      toast({
        title: "Entrou no grupo",
        description: "Você entrou no grupo familiar com sucesso.",
      });
    },
    onError: (error) => {
      toast({
        title: "Erro",
        description: error.message || "Erro ao entrar no grupo.",
        variant: "destructive",
      });
      console.error('Error joining family group:', error);
    },
  });

  return {
    familyGroups,
    isLoading,
    error,
    createGroup: createGroupMutation.mutate,
    joinGroup: joinGroupMutation.mutate,
    isCreatingGroup: createGroupMutation.isPending,
    isJoiningGroup: joinGroupMutation.isPending,
  };
};
