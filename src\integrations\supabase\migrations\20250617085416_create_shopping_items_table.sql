-- Migration para criar a tabela shopping_items
CREATE TABLE IF NOT EXISTS public.shopping_items (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  name text NOT NULL,
  quantity integer NOT NULL,
  unit text NOT NULL,
  category text NOT NULL,
  completed boolean NOT NULL DEFAULT false,
  min_stock integer,
  added_by uuid NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  
  -- Chaves estrangeiras
  CONSTRAINT shopping_items_pkey PRIMARY KEY (id),
  CONSTRAINT shopping_items_added_by_fkey FOREIGN KEY (added_by) 
    REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Criar índice para melhorar consultas por usuário
CREATE INDEX IF NOT EXISTS shopping_items_added_by_idx ON public.shopping_items (added_by);

-- <PERSON><PERSON>r trigger para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_shopping_items_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER shopping_items_updated_at_trigger
BEFORE UPDATE ON public.shopping_items
FOR EACH ROW
EXECUTE FUNCTION update_shopping_items_updated_at();

-- Habilitar RLS (Row Level Security)
ALTER TABLE public.shopping_items ENABLE ROW LEVEL SECURITY;

-- Criar políticas de segurança
CREATE POLICY "Permitir leitura para usuários autenticados" 
ON public.shopping_items
FOR SELECT
TO authenticated
USING (true);

CREATE POLICY "Permitir inserção para usuários autenticados"
ON public.shopping_items
FOR INSERT
TO authenticated
WITH CHECK (true);

CREATE POLICY "Permitir atualização apenas para donos"
ON public.shopping_items
FOR UPDATE
TO authenticated
USING (added_by = auth.uid());

CREATE POLICY "Permitir exclusão apenas para donos" 
ON public.shopping_items
FOR DELETE
TO authenticated
USING (added_by = auth.uid());