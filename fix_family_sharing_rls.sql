-- Fix Family Sharing RLS Policies
-- Execute this in Supabase SQL Editor to fix family sharing issues

-- First, let's check current policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename IN ('shopping_items', 'pantry_items', 'family_groups', 'family_members')
ORDER BY tablename, policyname;

-- Drop all existing policies to start fresh
DROP POLICY IF EXISTS "shopping_select_own" ON public.shopping_items;
DROP POLICY IF EXISTS "shopping_insert_own" ON public.shopping_items;
DROP POLICY IF EXISTS "shopping_update_own" ON public.shopping_items;
DROP POLICY IF EXISTS "shopping_delete_own" ON public.shopping_items;

DROP POLICY IF EXISTS "pantry_select_own" ON public.pantry_items;
DROP POLICY IF EXISTS "pantry_insert_own" ON public.pantry_items;
DROP POLICY IF EXISTS "pantry_update_own" ON public.pantry_items;
DROP POLICY IF EXISTS "pantry_delete_own" ON public.pantry_items;

DROP POLICY IF EXISTS "Family members can view shopping items" ON public.shopping_items;
DROP POLICY IF EXISTS "Family members can update shopping items" ON public.shopping_items;
DROP POLICY IF EXISTS "Family members can delete shopping items" ON public.shopping_items;
DROP POLICY IF EXISTS "Users can insert shopping items" ON public.shopping_items;

DROP POLICY IF EXISTS "Family members can view pantry items" ON public.pantry_items;
DROP POLICY IF EXISTS "Family members can update pantry items" ON public.pantry_items;
DROP POLICY IF EXISTS "Family members can delete pantry items" ON public.pantry_items;
DROP POLICY IF EXISTS "Users can insert pantry items" ON public.pantry_items;

-- Create new, simplified family sharing policies for shopping_items
CREATE POLICY "shopping_items_family_select" ON public.shopping_items
FOR SELECT USING (
  user_id = auth.uid() OR 
  user_id IN (
    SELECT fm2.user_id 
    FROM public.family_members fm1
    JOIN public.family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

CREATE POLICY "shopping_items_family_insert" ON public.shopping_items
FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "shopping_items_family_update" ON public.shopping_items
FOR UPDATE USING (
  user_id = auth.uid() OR 
  user_id IN (
    SELECT fm2.user_id 
    FROM public.family_members fm1
    JOIN public.family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

CREATE POLICY "shopping_items_family_delete" ON public.shopping_items
FOR DELETE USING (
  user_id = auth.uid() OR 
  user_id IN (
    SELECT fm2.user_id 
    FROM public.family_members fm1
    JOIN public.family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

-- Create new, simplified family sharing policies for pantry_items
CREATE POLICY "pantry_items_family_select" ON public.pantry_items
FOR SELECT USING (
  user_id = auth.uid() OR 
  user_id IN (
    SELECT fm2.user_id 
    FROM public.family_members fm1
    JOIN public.family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

CREATE POLICY "pantry_items_family_insert" ON public.pantry_items
FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "pantry_items_family_update" ON public.pantry_items
FOR UPDATE USING (
  user_id = auth.uid() OR 
  user_id IN (
    SELECT fm2.user_id 
    FROM public.family_members fm1
    JOIN public.family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

CREATE POLICY "pantry_items_family_delete" ON public.pantry_items
FOR DELETE USING (
  user_id = auth.uid() OR 
  user_id IN (
    SELECT fm2.user_id 
    FROM public.family_members fm1
    JOIN public.family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

-- Verify the new policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename IN ('shopping_items', 'pantry_items')
ORDER BY tablename, policyname;

-- Test query to see what shopping items a user can access
-- Replace 'USER_ID_HERE' with actual user ID for testing
/*
SELECT si.*, 'own' as access_type
FROM shopping_items si 
WHERE si.user_id = 'USER_ID_HERE'

UNION ALL

SELECT si.*, 'family' as access_type
FROM shopping_items si 
WHERE si.user_id IN (
  SELECT fm2.user_id 
  FROM family_members fm1
  JOIN family_members fm2 ON fm1.family_group_id = fm2.family_group_id
  WHERE fm1.user_id = 'USER_ID_HERE'
  AND fm2.user_id != 'USER_ID_HERE'
);
*/
