
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { FamilyInvitation } from '@/types';
import { useToast } from './use-toast';

export const useFamilyInvitations = (familyGroupId?: string) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: invitations = [], isLoading, error } = useQuery({
    queryKey: ['familyInvitations', familyGroupId],
    queryFn: async () => {
      if (!familyGroupId) return [];
      
      const { data, error } = await supabase
        .from('family_invitations')
        .select('*')
        .eq('family_group_id', familyGroupId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching family invitations:', error);
        throw error;
      }

      return data as FamilyInvitation[];
    },
    enabled: !!familyGroupId,
  });

  const sendInvitationMutation = useMutation({
    mutationFn: async (email: string) => {
      if (!user || !familyGroupId) throw new Error('Missing required data');
      
      const { data, error } = await supabase
        .from('family_invitations')
        .insert({
          family_group_id: familyGroupId,
          invited_by: user.id,
          email: email.toLowerCase(),
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['familyInvitations'] });
      toast({
        title: "Convite enviado",
        description: "Convite enviado com sucesso.",
      });
    },
    onError: (error) => {
      toast({
        title: "Erro",
        description: "Erro ao enviar convite.",
        variant: "destructive",
      });
      console.error('Error sending invitation:', error);
    },
  });

  const cancelInvitationMutation = useMutation({
    mutationFn: async (invitationId: string) => {
      const { error } = await supabase
        .from('family_invitations')
        .delete()
        .eq('id', invitationId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['familyInvitations'] });
      toast({
        title: "Convite cancelado",
        description: "Convite cancelado com sucesso.",
      });
    },
    onError: (error) => {
      toast({
        title: "Erro",
        description: "Erro ao cancelar convite.",
        variant: "destructive",
      });
      console.error('Error canceling invitation:', error);
    },
  });

  return {
    invitations,
    isLoading,
    error,
    sendInvitation: sendInvitationMutation.mutate,
    cancelInvitation: cancelInvitationMutation.mutate,
    isSendingInvitation: sendInvitationMutation.isPending,
    isCancelingInvitation: cancelInvitationMutation.isPending,
  };
};
