import { PullToRefresh } from "@/components/PullToRefresh";
import { TopHeader } from "@/components/TopHeader";
import { AppProvider } from "@/contexts/AppContext";
import { Navigation } from "@/components/Navigation";
import { useIsMobile } from "@/hooks/use-mobile";
import { DesktopSidebar } from "@/components/DesktopSidebar";
import { MobileNavigation } from "@/components/MobileNavigation";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AuthGuard } from "@/components/auth/AuthGuard";

const IndexContent = () => {
  const isMobile = useIsMobile();

  // Layout: mobile - header + main + bottom nav; desktop - header + sidebar + main.
  if (isMobile) {
    return (
      <>
        <TopHeader />
        <PullToRefresh onRefresh={async () => {
          // Lógica para atualizar os dados da aplicação
          window.location.reload();
        }}>
          <main className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50">
            <Navigation />
          </main>
        </PullToRefresh>
        <MobileNavigation />
      </>
    );
  }

  // Desktop
  return (
    <SidebarProvider>
      <div className="flex min-h-screen w-full">
        <TopHeader />
        <DesktopSidebar />
        <main className="flex-1 flex flex-col bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50">
          <Navigation />
        </main>
      </div>
    </SidebarProvider>
  );
};

const Index = () => {
  return (
    <AuthGuard>
      <AppProvider>
        <IndexContent />
      </AppProvider>
    </AuthGuard>
  );
};

export default Index;
