-- Test RLS policies directly in Supabase SQL Editor
-- Run these queries to test if policies are working correctly

-- 1. Check current user
SELECT auth.uid() as current_user_id;

-- 2. Test pantry_items access
SELECT 
  id, 
  name, 
  user_id, 
  created_at 
FROM pantry_items 
LIMIT 5;

-- 3. Test shopping_items access
SELECT 
  id, 
  name, 
  user_id, 
  created_at 
FROM shopping_items 
LIMIT 5;

-- 4. Test profiles access
SELECT 
  id, 
  full_name, 
  email 
FROM profiles 
WHERE id = auth.uid();

-- 5. Test family_groups access
SELECT 
  id, 
  name, 
  created_by, 
  invite_code 
FROM family_groups 
LIMIT 5;

-- 6. Test family_members access
SELECT 
  id, 
  family_group_id, 
  user_id, 
  role 
FROM family_members 
LIMIT 5;

-- 7. Check if RLS is enabled on tables
SELECT 
  schemaname, 
  tablename, 
  rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN ('pantry_items', 'shopping_items', 'profiles', 'family_groups', 'family_members');

-- 8. List all policies on our tables
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename IN ('pantry_items', 'shopping_items', 'profiles', 'family_groups', 'family_members')
ORDER BY tablename, policyname;

-- 9. Test direct insert (should work for own user)
-- INSERT INTO pantry_items (name, quantity, unit, category, user_id) 
-- VALUES ('Test Item', 1, 'un', 'Teste', auth.uid());

-- 10. Test if user has any family memberships
SELECT 
  fg.name as group_name,
  fm.role,
  fm.created_at
FROM family_members fm
JOIN family_groups fg ON fm.family_group_id = fg.id
WHERE fm.user_id = auth.uid();
