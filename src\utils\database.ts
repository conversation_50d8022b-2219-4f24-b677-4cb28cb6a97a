
import { DatabasePantryItem, DatabaseShoppingItem, PantryItem, ShoppingItem } from "@/types";

// Convert database pantry item to app format
export const convertDbPantryItem = (dbItem: DatabasePantryItem): PantryItem => ({
  id: dbItem.id,
  name: dbItem.name,
  quantity: dbItem.quantity,
  unit: dbItem.unit,
  category: dbItem.category,
  expiryDate: dbItem.expiry_date || '',
  addedDate: dbItem.created_at.split('T')[0],
  isLowStock: dbItem.is_low_stock,
  addedById: dbItem.user_id,
});

// Convert app pantry item to database format
export const convertToPantryDbFormat = (item: Omit<PantryItem, 'id' | 'addedDate' | 'addedById'>, userId: string) => ({
  name: item.name,
  quantity: item.quantity,
  unit: item.unit,
  category: item.category,
  expiry_date: item.expiryDate || null,
  is_low_stock: item.isLowStock,
  user_id: userId,
});

// Convert database shopping item to app format
export const convertDbShoppingItem = (dbItem: DatabaseShoppingItem): ShoppingItem => ({
  id: dbItem.id,
  name: dbItem.name,
  quantity: dbItem.quantity,
  unit: dbItem.unit,
  category: dbItem.category,
  completed: dbItem.checked,
  addedBy: dbItem.user_id, // This will need to be enhanced to show actual user names
});

// Convert app shopping item to database format
export const convertToShoppingDbFormat = (item: Omit<ShoppingItem, 'id' | 'addedBy'>, userId: string) => ({
  name: item.name,
  quantity: item.quantity,
  unit: item.unit,
  category: item.category,
  checked: item.completed,
  user_id: userId,
});
