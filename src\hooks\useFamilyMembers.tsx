
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { FamilyMember } from '@/types';
import { useToast } from './use-toast';

export const useFamilyMembers = (familyGroupId?: string) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: familyMembers = [], isLoading, error } = useQuery({
    queryKey: ['familyMembers', familyGroupId],
    queryFn: async () => {
      if (!familyGroupId) return [];
      
      const { data, error } = await supabase
        .from('family_members')
        .select(`
          *,
          profiles(full_name, email, avatar_url)
        `)
        .eq('family_group_id', familyGroupId)
        .order('joined_at', { ascending: true });

      if (error) {
        console.error('Error fetching family members:', error);
        throw error;
      }

      return data as (FamilyMember & { profiles: any })[];
    },
    enabled: !!familyGroupId,
  });

  const removeMemberMutation = useMutation({
    mutationFn: async (memberId: string) => {
      const { error } = await supabase
        .from('family_members')
        .delete()
        .eq('id', memberId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['familyMembers'] });
      toast({
        title: "Membro removido",
        description: "Membro removido do grupo com sucesso.",
      });
    },
    onError: (error) => {
      toast({
        title: "Erro",
        description: "Erro ao remover membro.",
        variant: "destructive",
      });
      console.error('Error removing family member:', error);
    },
  });

  const updateRoleMutation = useMutation({
    mutationFn: async ({ memberId, role }: { memberId: string, role: 'admin' | 'member' }) => {
      const { error } = await supabase
        .from('family_members')
        .update({ role })
        .eq('id', memberId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['familyMembers'] });
      toast({
        title: "Função atualizada",
        description: "Função do membro atualizada com sucesso.",
      });
    },
    onError: (error) => {
      toast({
        title: "Erro",
        description: "Erro ao atualizar função.",
        variant: "destructive",
      });
      console.error('Error updating member role:', error);
    },
  });

  return {
    familyMembers,
    isLoading,
    error,
    removeMember: removeMemberMutation.mutate,
    updateRole: updateRoleMutation.mutate,
    isRemovingMember: removeMemberMutation.isPending,
    isUpdatingRole: updateRoleMutation.isPending,
  };
};
