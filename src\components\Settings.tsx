
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Settings as SettingsIcon, Bell, Shield, Palette, User } from "lucide-react";
import { useState } from "react";
import { useAppContext } from "@/contexts/AppContext";
import { useAuth } from "@/hooks/useAuth";
import { supabase } from "@/integrations/supabase/client";

export function Settings() {
  const { notificationSettings, updateNotificationSettings } = useAppContext();
  const { user } = useAuth();
  const [usageData, setUsageData] = useState(false);
  const [theme, setTheme] = useState("Claro");
  const [isEditingName, setIsEditingName] = useState(false);
  const [isEditingEmail, setIsEditingEmail] = useState(false);
  const [newName, setNewName] = useState(user?.user_metadata?.full_name || "");
  const [newEmail, setNewEmail] = useState(user?.email || "");

  const handleNotificationChange = (type: 'expiry' | 'stock', value: boolean) => {
    updateNotificationSettings({
      [type]: value
    });
  };

  const handleThemeChange = () => {
    setTheme(prev => prev === "Claro" ? "Escuro" : "Claro");
  };

  const handleExportData = () => {
    // Simular exportação de dados
    const data = {
      pantryItems: "dados da despensa",
      shoppingItems: "dados da lista de compras",
      settings: { notificationSettings, theme, usageData }
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'meus-dados-pantrypal.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleUpdateName = async () => {
    if (!user) return;
    
    const { error } = await supabase.auth.updateUser({
      data: { full_name: newName }
    });

    if (error) {
      console.error("Erro ao atualizar nome:", error.message);
      alert("Erro ao atualizar nome");
      return;
    }

    setIsEditingName(false);
    alert("Nome atualizado com sucesso!");
  };

  const handleUpdateEmail = async () => {
    if (!user) return;
    
    const { error } = await supabase.auth.updateUser({
      email: newEmail
    });

    if (error) {
      console.error("Erro ao atualizar email:", error.message);
      alert("Erro ao atualizar email");
      return;
    }

    setIsEditingEmail(false);
    alert("Email atualizado com sucesso! Verifique sua caixa de entrada para confirmar.");
  };
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-2">
          <SettingsIcon className="w-6 h-6 text-green-600" />
          <h2 className="text-2xl font-bold text-gray-800">Configurações</h2>
        </div>
        <p className="text-gray-600">Personalize sua experiência no DespensAi</p>
      </div>

      {/* Perfil */}
      <Card className="bg-white shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <User className="w-5 h-5 text-green-600" />
            Perfil
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="w-full">
              <Label className="text-base">Nome de usuário</Label>
              {isEditingName ? (
                <div className="flex items-center gap-2 mt-1">
                  <Input
                    value={newName}
                    onChange={(e) => setNewName(e.target.value)}
                    className="flex-1"
                  />
                  <Button size="sm" onClick={handleUpdateName}>Salvar</Button>
                  <Button variant="outline" size="sm" onClick={() => setIsEditingName(false)}>Cancelar</Button>
                </div>
              ) : (
                <p className="text-sm text-gray-600 py-1">
                  {user?.user_metadata?.full_name || "Usuário Padrão"}
                </p>
              )}
            </div>
            {!isEditingName && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => {
                  setNewName(user?.user_metadata?.full_name || "");
                  setIsEditingName(true);
                }}
              >
                Editar
              </Button>
            )}
          </div>
          <Separator />
          <div className="flex items-center justify-between">
            <div className="w-full">
              <Label className="text-base">Email</Label>
              {isEditingEmail ? (
                <div className="flex items-center gap-2 mt-1">
                  <Input
                    type="email"
                    value={newEmail}
                    onChange={(e) => setNewEmail(e.target.value)}
                    className="flex-1"
                  />
                  <Button size="sm" onClick={handleUpdateEmail}>Salvar</Button>
                  <Button variant="outline" size="sm" onClick={() => setIsEditingEmail(false)}>Cancelar</Button>
                </div>
              ) : (
                <p className="text-sm text-gray-600 py-1">{user?.email || "<EMAIL>"}</p>
              )}
            </div>
            {!isEditingEmail && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => {
                  setNewEmail(user?.email || "");
                  setIsEditingEmail(true);
                }}
              >
                Alterar
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Notificações */}
      <Card className="bg-white shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <Bell className="w-5 h-5 text-green-600" />
            Notificações
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="expiry-notifications" className="text-base">Avisos de vencimento</Label>
              <p className="text-sm text-gray-600">Receba notificações quando itens estiverem próximos do vencimento</p>
            </div>
            <Switch
              id="expiry-notifications"
              checked={notificationSettings.expiry}
              onCheckedChange={(checked) => handleNotificationChange('expiry', checked)}
            />
          </div>
          <Separator />
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="stock-notifications" className="text-base">Alertas de estoque baixo</Label>
              <p className="text-sm text-gray-600">Notificações quando o estoque estiver baixo</p>
            </div>
            <Switch
              id="stock-notifications"
              checked={notificationSettings.stock}
              onCheckedChange={(checked) => handleNotificationChange('stock', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Aparência */}
      <Card className="bg-white shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <Palette className="w-5 h-5 text-green-600" />
            Aparência
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-base">Tema</Label>
              <p className="text-sm text-gray-600">Personalize a aparência do aplicativo</p>
            </div>
            <Button variant="outline" size="sm" onClick={handleThemeChange}>{theme}</Button>
          </div>
        </CardContent>
      </Card>

      {/* Privacidade */}
      <Card className="bg-white shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <Shield className="w-5 h-5 text-green-600" />
            Privacidade e Segurança
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-base">Compartilhar dados de uso</Label>
              <p className="text-sm text-gray-600">Ajude-nos a melhorar o aplicativo compartilhando dados anônimos</p>
            </div>
            <Switch
              id="usage-data"
              checked={usageData}
              onCheckedChange={setUsageData}
            />
          </div>
          <Separator />
          <Button variant="outline" className="w-full" onClick={handleExportData}>
            Exportar meus dados
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};
