-- Simple fix for family sharing - focus on the core issue
-- Execute this in Supabase SQL Editor

-- ============================================================================
-- STEP 1: Remove conflicting policies
-- ============================================================================

-- Remove all pantry_items policies
DROP POLICY IF EXISTS "Users can view family pantry items" ON public.pantry_items;
DROP POLICY IF EXISTS "Family members can view pantry items" ON public.pantry_items;
DROP POLICY IF EXISTS "pantry_select_own" ON public.pantry_items;
DROP POLICY IF EXISTS "pantry_family_access" ON public.pantry_items;

-- Remove all shopping_items policies  
DROP POLICY IF EXISTS "Users can view family shopping items" ON public.shopping_items;
DROP POLICY IF EXISTS "Family members can view shopping items" ON public.shopping_items;
DROP POLICY IF EXISTS "shopping_select_own" ON public.shopping_items;
DROP POLICY IF EXISTS "shopping_family_access" ON public.shopping_items;

-- ============================================================================
-- STEP 2: Create simple working policies
-- ============================================================================

-- PANTRY ITEMS: Simple family sharing policy
CREATE POLICY "pantry_family_sharing" ON public.pantry_items
FOR SELECT USING (
  user_id = auth.uid() OR user_id IN (
    SELECT fm2.user_id
    FROM family_members fm1
    JOIN family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

-- SHOPPING ITEMS: Simple family sharing policy
CREATE POLICY "shopping_family_sharing" ON public.shopping_items
FOR SELECT USING (
  user_id = auth.uid() OR user_id IN (
    SELECT fm2.user_id
    FROM family_members fm1
    JOIN family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

-- ============================================================================
-- STEP 3: Ensure other necessary policies exist
-- ============================================================================

-- Make sure users can insert their own items
CREATE POLICY IF NOT EXISTS "pantry_insert_own" ON public.pantry_items
FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY IF NOT EXISTS "shopping_insert_own" ON public.shopping_items
FOR INSERT WITH CHECK (user_id = auth.uid());

-- Make sure users can update family items
CREATE POLICY IF NOT EXISTS "pantry_update_family" ON public.pantry_items
FOR UPDATE USING (
  user_id = auth.uid() OR user_id IN (
    SELECT fm2.user_id
    FROM family_members fm1
    JOIN family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

CREATE POLICY IF NOT EXISTS "shopping_update_family" ON public.shopping_items
FOR UPDATE USING (
  user_id = auth.uid() OR user_id IN (
    SELECT fm2.user_id
    FROM family_members fm1
    JOIN family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

-- Make sure users can delete family items
CREATE POLICY IF NOT EXISTS "pantry_delete_family" ON public.pantry_items
FOR DELETE USING (
  user_id = auth.uid() OR user_id IN (
    SELECT fm2.user_id
    FROM family_members fm1
    JOIN family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

CREATE POLICY IF NOT EXISTS "shopping_delete_family" ON public.shopping_items
FOR DELETE USING (
  user_id = auth.uid() OR user_id IN (
    SELECT fm2.user_id
    FROM family_members fm1
    JOIN family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

-- ============================================================================
-- STEP 4: Test the fix
-- ============================================================================

-- This should return items from both users if family sharing is working
SELECT 
  'Test Result' as status,
  COUNT(*) as total_pantry_items_visible,
  COUNT(DISTINCT user_id) as different_owners
FROM pantry_items;

SELECT 
  'Test Result' as status,
  COUNT(*) as total_shopping_items_visible,
  COUNT(DISTINCT user_id) as different_owners
FROM shopping_items;
