
import { useRef, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Bell, AlertTriangle, Calendar, ShoppingCart, Clock, X } from "lucide-react";
import { useNotifications } from "@/hooks/useNotifications";

interface NotificationModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const NotificationModal = ({ isOpen, onClose }: NotificationModalProps) => {
  const { notifications, markAsRead, clearAll } = useNotifications();

  const getIcon = (type: string) => {
    switch (type) {
      case 'expiring':
        return <Calendar className="w-4 h-4 text-orange-600" />;
      case 'lowStock':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      case 'shoppingList':
        return <ShoppingCart className="w-4 h-4 text-blue-600" />;
      default:
        return <Bell className="w-4 h-4 text-gray-600" />;
    }
  };

  const recentNotifications = notifications.slice(0, 10);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="max-w-md max-h-[90vh] p-0 overflow-hidden mx-4 my-6 rounded-2xl sm:rounded-2xl w-[calc(100vw-2rem)] sm:w-full"
        style={{
          margin: '1.5rem auto',
          position: 'fixed',
          left: '50%',
          top: '50%',
          transform: 'translate(-50%, -50%)',
          right: 'auto',
          bottom: 'auto'
        }}
      >
        <div className="bg-white rounded-2xl w-full h-full flex flex-col relative max-h-[90vh]">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b bg-gradient-to-r from-green-50 to-emerald-50">
            <div className="flex items-center gap-3">
              <Bell className="w-6 h-6 text-green-600" />
              <div>
                <h2 className="text-xl font-bold text-gray-800">Notificações</h2>
                <p className="text-sm text-gray-600">Suas atualizações e alertas</p>
              </div>
            </div>
            <Button variant="ghost" size="sm" onClick={clearAll} className="text-sm h-8 px-3 hover:bg-white/50">
              Limpar Todas
            </Button>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto flex-1 min-h-0">
          {recentNotifications.length === 0 ? (
            <div className="text-center py-12 px-4">
              <Bell className="w-12 h-12 mx-auto text-gray-300 mb-3" />
              <p className="text-base text-gray-500 mb-1">Nenhuma notificação</p>
              <p className="text-sm text-gray-400">Você está em dia com tudo!</p>
            </div>
          ) : (
            <div className="space-y-1">
              {recentNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 hover:bg-gray-50 cursor-pointer border-l-4 transition-colors ${
                    !notification.read 
                      ? 'border-l-green-500 bg-green-50/30' 
                      : 'border-l-transparent'
                  }`}
                  onClick={() => markAsRead(notification.id)}
                >
                  <div className="flex items-start gap-3">
                    <div className="mt-1">
                      {getIcon(notification.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-medium text-gray-800 text-sm">
                          {notification.title}
                        </h4>
                        {!notification.read && (
                          <Badge variant="secondary" className="text-xs px-2 py-0.5">
                            Nova
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mb-2 leading-relaxed">
                        {notification.message}
                      </p>
                      <div className="flex items-center gap-1 text-xs text-gray-400">
                        <Clock className="w-3 h-3" />
                        {new Date(notification.timestamp).toLocaleDateString('pt-BR', {
                          day: '2-digit',
                          month: '2-digit',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
