
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Package, ShoppingCart, Plus, Users, Settings } from "lucide-react";
import { useAppContext } from "@/contexts/AppContext";

const TABS = [
  {
    value: "pantry",
    label: "Despensa",
    icon: Package
  },
  {
    value: "shopping",
    label: "Compras",
    icon: ShoppingCart
  },
  {
    value: "add",
    label: "Adicionar",
    icon: Plus
  },
  {
    value: "family",
    label: "Família",
    icon: Users
  },
  {
    value: "settings",
    label: "Configurações",
    icon: Settings
  }
];

export function DesktopSidebar() {
  const { activeTab, setActiveTab } = useAppContext();

  return (
    <Sidebar>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Menu</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {TABS.map(tab => (
                <SidebarMenuItem key={tab.value}>
                  <SidebarMenuButton
                    isActive={activeTab === tab.value}
                    onClick={() => setActiveTab(tab.value)}
                  >
                    <tab.icon size={20} />
                    <span>{tab.label}</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
