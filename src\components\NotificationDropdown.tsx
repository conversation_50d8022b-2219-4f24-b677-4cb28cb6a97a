
import { useRef, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON>le } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Bell, AlertTriangle, Calendar, ShoppingCart, Clock, X } from "lucide-react";
import { useNotifications } from "@/hooks/useNotifications";

interface NotificationDropdownProps {
  onClose: () => void;
}

export const NotificationDropdown = ({ onClose }: NotificationDropdownProps) => {
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { notifications, markAsRead, clearAll } = useNotifications();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [onClose]);

  const getIcon = (type: string) => {
    switch (type) {
      case 'expiring':
        return <Calendar className="w-4 h-4 text-orange-600" />;
      case 'lowStock':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      case 'shoppingList':
        return <ShoppingCart className="w-4 h-4 text-blue-600" />;
      default:
        return <Bell className="w-4 h-4 text-gray-600" />;
    }
  };

  const recentNotifications = notifications.slice(0, 5);

  return (
    <div
      ref={dropdownRef}
      className="absolute right-0 top-12 w-80 max-w-[90vw] bg-white border border-gray-200 rounded-lg shadow-lg z-50"
    >
      <Card className="border-0 shadow-none">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">Notificações</CardTitle>
            <div className="flex items-center gap-1">
              <Button variant="ghost" size="sm" onClick={clearAll} className="text-xs h-6 px-2">
                Limpar
              </Button>
              <Button variant="ghost" size="icon" onClick={onClose} className="h-6 w-6">
                <X className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0 max-h-80 overflow-y-auto">
          {recentNotifications.length === 0 ? (
            <div className="text-center py-8 px-4">
              <Bell className="w-8 h-8 mx-auto text-gray-300 mb-2" />
              <p className="text-sm text-gray-500">Nenhuma notificação</p>
            </div>
          ) : (
            <div className="space-y-1">
              {recentNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-3 hover:bg-gray-50 cursor-pointer border-l-2 ${
                    !notification.read 
                      ? 'border-l-green-500 bg-green-50/30' 
                      : 'border-l-transparent'
                  }`}
                  onClick={() => markAsRead(notification.id)}
                >
                  <div className="flex items-start gap-2">
                    {getIcon(notification.type)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-gray-800 text-xs">
                          {notification.title}
                        </h4>
                        {!notification.read && (
                          <Badge variant="secondary" className="text-xs px-1 py-0 h-4">
                            Nova
                          </Badge>
                        )}
                      </div>
                      <p className="text-xs text-gray-600 mb-1">
                        {notification.message}
                      </p>
                      <div className="flex items-center gap-1 text-xs text-gray-400">
                        <Clock className="w-3 h-3" />
                        {new Date(notification.timestamp).toLocaleDateString('pt-BR')}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
