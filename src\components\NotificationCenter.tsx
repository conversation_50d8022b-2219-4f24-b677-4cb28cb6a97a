
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Bell, AlertTriangle, Calendar, ShoppingCart, Clock, X } from "lucide-react";
import { useNotifications } from "@/hooks/useNotifications";

export const NotificationCenter = () => {
  const { notifications, markAsRead, clearAll, requestPermission, hasPermission } = useNotifications();

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Bell className="w-6 h-6 text-green-600" />
          <h2 className="text-2xl font-bold text-gray-800">Notificações</h2>
        </div>
        <Button variant="outline" size="sm" onClick={clearAll}>
          Limpar todas
        </Button>
      </div>

      {/* Permission Request */}
      {!hasPermission && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Bell className="w-5 h-5 text-blue-600" />
              <div className="flex-1">
                <p className="text-sm font-medium text-blue-800">
                  Ativar notificações push
                </p>
                <p className="text-xs text-blue-600">
                  Receba alertas sobre produtos vencendo e estoque baixo
                </p>
              </div>
              <Button size="sm" onClick={requestPermission}>
                Ativar
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Notifications List */}
      <div className="space-y-3">
        {notifications.length === 0 ? (
          <Card className="bg-white shadow-sm">
            <CardContent className="text-center py-12">
              <Bell className="w-12 h-12 mx-auto text-gray-300 mb-4" />
              <h3 className="text-lg font-medium text-gray-600 mb-2">
                Nenhuma notificação
              </h3>
              <p className="text-gray-500">
                Você será notificado sobre produtos vencendo e estoque baixo
              </p>
            </CardContent>
          </Card>
        ) : (
          notifications.map((notification) => {
            const getIcon = () => {
              switch (notification.type) {
                case 'expiring':
                  return <Calendar className="w-5 h-5 text-orange-600" />;
                case 'lowStock':
                  return <AlertTriangle className="w-5 h-5 text-red-600" />;
                case 'shoppingList':
                  return <ShoppingCart className="w-5 h-5 text-blue-600" />;
                default:
                  return <Bell className="w-5 h-5 text-gray-600" />;
              }
            };

            const getBgColor = () => {
              if (!notification.read) {
                switch (notification.type) {
                  case 'expiring':
                    return 'bg-orange-50 border-orange-200';
                  case 'lowStock':
                    return 'bg-red-50 border-red-200';
                  case 'shoppingList':
                    return 'bg-blue-50 border-blue-200';
                  default:
                    return 'bg-gray-50 border-gray-200';
                }
              }
              return 'bg-white';
            };

            return (
              <Card key={notification.id} className={`${getBgColor()} shadow-sm`}>
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    {getIcon()}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-gray-800 text-sm">
                          {notification.title}
                        </h4>
                        {!notification.read && (
                          <Badge variant="secondary" className="text-xs px-1 py-0">
                            Nova
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mb-2">
                        {notification.message}
                      </p>
                      <div className="flex items-center gap-2 text-xs text-gray-500">
                        <Clock className="w-3 h-3" />
                        {new Date(notification.timestamp).toLocaleString('pt-BR')}
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => markAsRead(notification.id)}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>
    </div>
  );
};
