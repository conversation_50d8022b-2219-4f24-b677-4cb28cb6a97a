import { useAppContext } from "@/contexts/AppContext";
import { useNavigate } from "react-router-dom";
import { Package, ShoppingCart, Users, Plus, Settings } from "lucide-react";
import { PantryDashboard } from "@/components/PantryDashboard";
import { ShoppingList } from "@/components/ShoppingList";
import { FamilyGroup } from "@/components/FamilyGroup";
import { Settings as SettingsTab } from "@/components/Settings";
import { AddItemSelectionModal } from "@/components/AddItemSelectionModal";
import { useState } from "react";
const TABS = [{
  value: "pantry",
  label: "Despensa",
  icon: Package
}, {
  value: "shopping",
  label: "Compras",
  icon: ShoppingCart
}, {
  value: "add",
  label: "Adicionar",
  icon: Plus
}, {
  value: "family",
  label: "Família",
  icon: Users
}, {
  value: "settings",
  label: "Configurações",
  icon: Settings
}];
export const Navigation = () => {
  const navigate = useNavigate();
  const [showSelectionModal, setShowSelectionModal] = useState(false);
  console.log('Modal visibility state:', showSelectionModal);
  const {
    activeTab,
    setActiveTab,
    pantryItems,
    shoppingItems,
    updatePantryItem,
    deletePantryItem,
    toggleShoppingItem,
    deleteShoppingItem,
    addShoppingItem,
    addShoppingItems,
    saveToPantry,
    clearCompletedItems,
    addPantryItem,
    setShowAddShoppingForm,
    setShowAddPantryForm
  } = useAppContext();

  // Render tab contents
  const renderTabContent = () => {
    switch (activeTab) {
      case "pantry":
        return <PantryDashboard items={pantryItems} onUpdateItem={updatePantryItem} onDeleteItem={deletePantryItem} onAddItem={addPantryItem} />;
      case "shopping":
        return <ShoppingList items={shoppingItems} onToggleItem={toggleShoppingItem} onDeleteItem={deleteShoppingItem} onAddItem={addShoppingItem} onAddItems={addShoppingItems} onSaveToPantry={saveToPantry} onClearCompleted={clearCompletedItems} />;
      case "family":
        return <FamilyGroup />;
      case "settings":
        return <SettingsTab />;
      default:
        return null;
    }
  };

  // Navigation bar
  return <>
      {/* Header */}
      <header className="fixed top-0 left-0 right-0 z-40 bg-white/95 backdrop-blur-xl shadow-sm border-b border-green-100">
        <div className="container mx-auto px-4 py-3 max-w-4xl">
          <div className="flex items-center justify-center">
            <div className="flex items-center gap-2">
              <img src="https://i.postimg.cc/mrFXt52R/logo-despensa-removebg.png" alt="PantryPal" className="w-8 h-8 rounded-lg" />
              <h1 className="text-xl font-bold text-green-700">PantryPal</h1>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-6 max-w-4xl pb-28 pt-20 min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50">
        {renderTabContent()}
      </main>

      <AddItemSelectionModal isOpen={showSelectionModal} onClose={() => setShowSelectionModal(false)} onSelectList={() => {
      setShowSelectionModal(false);
      setActiveTab("shopping");
      setShowAddShoppingForm(true);
    }} onSelectPantry={() => {
      setShowSelectionModal(false);
      setActiveTab("pantry");
      setShowAddPantryForm(true);
    }} />

      <nav className="fixed inset-x-0 bottom-0 z-50">
        <div className="w-full bg-gradient-to-t from-white/95 via-white/90 to-white/80 backdrop-blur-xl shadow-[0_-4px_32px_0_rgba(0,0,0,0.08)] border-t border-green-100 rounded-t-3xl pt-2 pb-3 py-[13px] my-0 mx-[8px] px-0">
          <ul className="flex justify-between items-end">
            {TABS.map(tab => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.value;
            const isAddButton = tab.value === "add" || tab.label === "Adicionar";
            return <li key={tab.value} className={isAddButton ? "relative flex-1 flex flex-col items-center z-10" : "flex-1 flex flex-col items-center"}>
                  <button aria-label={tab.label} onClick={() => {
                console.log('Add button clicked', {
                  value: tab.value,
                  label: tab.label
                });
                if (isAddButton) {
                  console.log('Setting modal to true');
                  setShowSelectionModal(true);
                } else {
                  if (tab.value === 'settings') {
                    navigate('/settings');
                  } else {
                    navigate('/');
                    setActiveTab(tab.value);
                  }
                }
              }} className={isAddButton ? `
                      transition-all duration-300
                      flex flex-col items-center justify-center
                      mt-[-24px] mb-0 rounded-full shadow-lg active:scale-95
                      bg-green-500 text-white hover:bg-green-600
                      w-14 h-14 border-3 border-white
                    ` : `
                      transition-all duration-200
                      flex flex-col items-center justify-center
                      py-2 px-1 rounded-xl active:scale-95 relative
                      ${isActive ? "bg-green-50 text-green-700 shadow-sm" : "text-gray-400 hover:bg-green-50/50 hover:text-green-600"}
                      w-full max-w-[64px] min-h-[52px]
                    `} style={isAddButton ? {
                boxShadow: "0 4px 16px 2px #22c55e25"
              } : {}}>
                    <div className="relative">
                      <Icon size={20} strokeWidth={2.2} />
                    </div>
                    <span className={`text-[10px] mt-1 font-medium leading-tight text-center ${isActive ? "text-green-700" : "text-gray-500"}`}>
                      {tab.label}
                    </span>
                  </button>
                </li>;
          })}
          </ul>
        </div>
      </nav>
    </>;
};
