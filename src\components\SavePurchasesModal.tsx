
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { ShoppingItem, PantryItem } from "@/types";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { useQueryClient } from "@tanstack/react-query";

interface SavePurchasesModalProps {
  isOpen: boolean;
  onClose: () => void;
  completedItems: ShoppingItem[];
  onSaveToPantry: (items: PantryItem[]) => Promise<void>;
  onClearCompleted: () => Promise<void>;
  onNavigateToPantry?: () => void;
}

export const SavePurchasesModal = ({
  isOpen,
  onClose,
  completedItems,
  onSaveToPantry,
  onClearCompleted,
  onNavigateToPantry
}: SavePurchasesModalProps) => {
  const { toast } = useToast();
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [itemsWithExpiry, setItemsWithExpiry] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);

  const handleExpiryChange = (itemId: string, expiryDate: string) => {
    setItemsWithExpiry(prev => ({
      ...prev,
      [itemId]: expiryDate
    }));
  };

  const handleSave = async () => {
    setIsSaving(true);

    const pantryItems: PantryItem[] = completedItems.map(item => ({
      id: Date.now().toString() + Math.random(),
      name: item.name,
      quantity: item.quantity,
      unit: item.unit,
      category: item.category,
      expiryDate: itemsWithExpiry[item.id] || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 dias por padrão
      addedDate: new Date().toISOString().split('T')[0],
      isLowStock: false,
      addedById: user?.id || "unknown-user"
    }));

    console.log('💾 SavePurchasesModal: Starting save process...');

    // Save items to pantry
    console.log('💾 Saving items to pantry...');
    await onSaveToPantry(pantryItems);
    console.log('✅ Items saved to pantry successfully');

    // Clear completed items from shopping list
    console.log('🗑️ Clearing completed items from shopping list...');
    await onClearCompleted();
    console.log('✅ Completed items cleared successfully');

    // Force manual cache invalidation for both pantry and shopping items
    console.log('🔄 Forcing manual cache invalidation...');
    await queryClient.invalidateQueries({ queryKey: ['pantryItems', user?.id] });
    await queryClient.invalidateQueries({ queryKey: ['shoppingItems', user?.id] });
    await queryClient.refetchQueries({ queryKey: ['pantryItems', user?.id] });
    await queryClient.refetchQueries({ queryKey: ['shoppingItems', user?.id] });
    console.log('✅ Cache invalidation completed');

    // Reset form state
    setItemsWithExpiry({});

    // Close modal
    onClose();

    // Show success toast
    toast({
      title: "Compras Salvas!",
      description: `${completedItems.length} ${completedItems.length === 1 ? 'item foi adicionado' : 'itens foram adicionados'} à despensa. Redirecionando...`,
    });

    // Navigate to pantry immediately
    if (onNavigateToPantry) {
      console.log('📋 Navigating to pantry...');
      onNavigateToPantry();

      // Show additional toast confirming navigation
      setTimeout(() => {
        toast({
          title: "Despensa Atualizada!",
          description: "Você pode ver seus novos itens na despensa.",
        });
      }, 200);
    }

    setIsSaving(false);
    console.log('✅ Save process completed successfully');
  };

  if (completedItems.length === 0) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Salvar Compras na Despensa</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <p className="text-sm text-gray-600">
            Defina as datas de validade para os itens comprados:
          </p>
          
          {completedItems.map((item) => (
            <div key={item.id} className="p-3 bg-gray-50 rounded-lg">
              <div className="flex justify-between items-center mb-2">
                <h4 className="font-medium">{item.name}</h4>
                <span className="text-sm text-gray-500">{item.quantity} {item.unit}</span>
              </div>
              <div className="space-y-1">
                <Label htmlFor={`expiry-${item.id}`} className="text-xs">Data de Validade</Label>
                <Input
                  id={`expiry-${item.id}`}
                  type="date"
                  value={itemsWithExpiry[item.id] || ''}
                  onChange={(e) => handleExpiryChange(item.id, e.target.value)}
                  className="text-sm"
                />
              </div>
            </div>
          ))}
          
          <div className="flex gap-2 pt-4">
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1"
              disabled={isSaving}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleSave}
              className="flex-1 bg-green-600 hover:bg-green-700"
              disabled={isSaving}
            >
              {isSaving ? "Salvando..." : "Salvar na Despensa"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
