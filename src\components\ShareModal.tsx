import React from 'react';
import { Dialog, DialogContent } from "@/components/ui/dialog";

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  children?: React.ReactNode;
}

export function ShareModal({ isOpen, onClose, children }: ShareModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <div className="bg-white dark:bg-gray-800 rounded-2xl w-full h-full flex flex-col relative">
          {children}
        </div>
      </DialogContent>
    </Dialog>
  );
}
