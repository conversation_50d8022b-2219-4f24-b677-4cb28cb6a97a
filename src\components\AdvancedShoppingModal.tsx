import { AdvancedShoppingForm } from "./AdvancedShoppingForm";
import { ShoppingItem } from "@/types";
import { useAppContext } from "@/contexts/AppContext";
import { useToast } from "@/hooks/use-toast";
import { useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";

interface AdvancedShoppingModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const AdvancedShoppingModal = ({ isOpen, onClose }: AdvancedShoppingModalProps) => {
  const { addShoppingItems, setActiveTab } = useAppContext();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user } = useAuth();

  const handleAddItems = async (newItems: Omit<ShoppingItem, 'id' | 'addedBy'>[]) => {
    try {
      console.log('🎯 AdvancedShoppingModal: Starting to add items');
      console.log('📦 Items to add:', newItems);
      console.log('🔢 Number of items:', newItems.length);

      // Validate items before adding
      if (!newItems || newItems.length === 0) {
        console.warn('⚠️ No items to add');
        toast({
          title: "Aviso",
          description: "Nenhum item para adicionar.",
          variant: "default",
        });
        return;
      }

      // Add items to the shopping list and wait for completion
      console.log('🚀 Calling addShoppingItems and waiting for completion...');
      await addShoppingItems(newItems);
      console.log('✅ addShoppingItems completed successfully');

      // Force manual cache invalidation and refetch
      console.log('🔄 Forcing manual cache invalidation...');
      await queryClient.invalidateQueries({ queryKey: ['shoppingItems', user?.id] });
      await queryClient.refetchQueries({ queryKey: ['shoppingItems', user?.id] });
      console.log('✅ Manual cache invalidation completed');

      // Show success toast
      toast({
        title: "Lista Atualizada!",
        description: `${newItems.length} ${newItems.length === 1 ? 'item adicionado' : 'itens adicionados'} à lista de compras.`,
      });

      // Navigate to shopping tab immediately
      console.log('📋 Setting shopping tab as active...');
      setActiveTab("shopping");

      // Close the modal after a short delay
      setTimeout(() => {
        console.log('🔒 Closing modal...');
        onClose();
      }, 200);

      console.log('✅ AdvancedShoppingModal: Process completed successfully');
    } catch (error) {
      console.error('❌ Error in handleAddItems:', error);
      toast({
        title: "Erro",
        description: "Erro ao adicionar itens à lista.",
        variant: "destructive",
      });
    }
  };

  const handleClose = () => {
    onClose();
  };

  return (
    <AdvancedShoppingForm
      isOpen={isOpen}
      onAddItems={handleAddItems}
      onClose={handleClose}
    />
  );
};
