import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";

export const TestSupabaseConnection = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<any[]>([]);

  const testDirectInsert = async () => {
    setIsLoading(true);
    setResults([]);
    
    try {
      console.log('🧪 Testing direct Supabase insert');
      console.log('👤 Current user:', user);
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      const testItem = {
        name: 'Teste Item ' + Date.now(),
        quantity: 1,
        unit: 'unidades',
        category: 'Teste',
        checked: false,
        user_id: user.id,
      };

      console.log('📦 Test item to insert:', testItem);

      const { data, error } = await supabase
        .from('shopping_items')
        .insert(testItem)
        .select()
        .single();

      console.log('📊 Insert result:', { data, error });

      if (error) {
        console.error('❌ Insert error:', error);
        throw error;
      }

      console.log('✅ Insert successful:', data);
      setResults(prev => [...prev, { type: 'success', message: 'Item inserido com sucesso!', data }]);
      
      toast({
        title: "Sucesso!",
        description: "Item de teste inserido com sucesso.",
      });

    } catch (error: any) {
      console.error('❌ Test failed:', error);
      setResults(prev => [...prev, { type: 'error', message: error.message, error }]);
      
      toast({
        title: "Erro",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testQuery = async () => {
    setIsLoading(true);
    
    try {
      console.log('🔍 Testing query');
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('shopping_items')
        .select('*')
        .eq('user_id', user.id)
        .limit(5);

      console.log('📊 Query result:', { data, error });

      if (error) {
        throw error;
      }

      setResults(prev => [...prev, { type: 'info', message: `Encontrados ${data?.length || 0} itens`, data }]);
      
    } catch (error: any) {
      console.error('❌ Query failed:', error);
      setResults(prev => [...prev, { type: 'error', message: error.message, error }]);
    } finally {
      setIsLoading(false);
    }
  };

  const clearResults = () => {
    setResults([]);
  };

  if (!user) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Teste Supabase</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Usuário não autenticado</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Teste Conexão Supabase</CardTitle>
        <p className="text-sm text-gray-600">Usuário: {user.email}</p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button 
            onClick={testDirectInsert} 
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Testar Insert
          </Button>
          <Button 
            onClick={testQuery} 
            disabled={isLoading}
            variant="outline"
          >
            Testar Query
          </Button>
          <Button 
            onClick={clearResults} 
            variant="outline"
          >
            Limpar
          </Button>
        </div>

        {results.length > 0 && (
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {results.map((result, index) => (
              <div 
                key={index} 
                className={`p-2 rounded text-sm ${
                  result.type === 'success' ? 'bg-green-100 text-green-800' :
                  result.type === 'error' ? 'bg-red-100 text-red-800' :
                  'bg-blue-100 text-blue-800'
                }`}
              >
                <div className="font-medium">{result.message}</div>
                {result.data && (
                  <pre className="text-xs mt-1 overflow-x-auto">
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
