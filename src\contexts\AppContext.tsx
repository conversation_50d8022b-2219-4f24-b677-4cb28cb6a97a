
import React, { createContext, useContext, useState } from 'react';
import { PantryItem, ShoppingItem } from '@/types';
import { usePantryItems } from '@/hooks/usePantryItems';
import { useShoppingItems } from '@/hooks/useShoppingItems';

interface NotificationSettings {
  expiry: boolean;
  stock: boolean;
}

interface AppContextType {
  pantryItems: PantryItem[];
  shoppingItems: ShoppingItem[];
  activeTab: string;
  setActiveTab: (tab: string) => void;
  showAddShoppingForm: boolean;
  setShowAddShoppingForm: (show: boolean) => void;
  showAddPantryForm: boolean;
  setShowAddPantryForm: (show: boolean) => void;
  addPantryItem: (item: Omit<PantryItem, 'id' | 'addedDate' | 'addedById'>) => void;
  updatePantryItem: (id: string, updates: Partial<PantryItem>) => void;
  deletePantryItem: (id: string) => void;
  addShoppingItem: (item: Omit<ShoppingItem, 'id' | 'addedBy'>) => void;
  addShoppingItems: (items: Omit<ShoppingItem, 'id' | 'addedBy'>[]) => Promise<void>;
  toggleShoppingItem: (id: string) => Promise<void>;
  deleteShoppingItem: (id: string) => Promise<void>;
  saveToPantry: (items: PantryItem[]) => Promise<void>;
  clearCompletedItems: () => Promise<void>;
  notificationSettings: NotificationSettings;
  updateNotificationSettings: (settings: Partial<NotificationSettings>) => void;
  isLoadingPantry: boolean;
  isLoadingShopping: boolean;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export const useAppContext = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};

interface AppProviderProps {
  children: React.ReactNode;
}

export const AppProvider = ({ children }: AppProviderProps) => {
  const [activeTab, setActiveTab] = useState("pantry");
  const [showAddShoppingForm, setShowAddShoppingForm] = useState(false);
  const [showAddPantryForm, setShowAddPantryForm] = useState(false);
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    expiry: true,
    stock: true
  });

  // Use custom hooks for data management
  const {
    pantryItems,
    isLoading: isLoadingPantry,
    addItem: addPantryItemMutation,
    addItemAsync: addPantryItemAsyncMutation,
    updateItem: updatePantryItemMutation,
    deleteItem: deletePantryItemMutation,
  } = usePantryItems();

  const {
    shoppingItems,
    isLoading: isLoadingShopping,
    addItem: addShoppingItemMutation,
    addMultipleItems: addMultipleShoppingItemsMutation,
    toggleItem: toggleShoppingItemMutation,
    deleteItem: deleteShoppingItemMutation,
    clearCompleted: clearCompletedItemsMutation,
  } = useShoppingItems();

  // Debug log to check shopping items (can be removed in production)
  // console.log('🛒 AppContext: shoppingItems:', shoppingItems);
  // console.log('🔢 AppContext: shoppingItems length:', shoppingItems?.length);

  const addPantryItem = (item: Omit<PantryItem, 'id' | 'addedDate' | 'addedById'>) => {
    addPantryItemMutation(item);
  };

  const updatePantryItem = (id: string, updates: Partial<PantryItem>) => {
    updatePantryItemMutation({ id, updates });
  };

  const deletePantryItem = (id: string) => {
    deletePantryItemMutation(id);
  };

  const addShoppingItem = (item: Omit<ShoppingItem, 'id' | 'addedBy'>) => {
    addShoppingItemMutation(item);
  };

  const addShoppingItems = async (items: Omit<ShoppingItem, 'id' | 'addedBy'>[]) => {
    console.log('🏪 AppContext: addShoppingItems called');
    console.log('📦 Items received:', items);
    console.log('🔧 Calling addMultipleShoppingItemsMutation...');
    await addMultipleShoppingItemsMutation(items);
  };

  const toggleShoppingItem = async (id: string) => {
    await toggleShoppingItemMutation(id);
  };

  const deleteShoppingItem = async (id: string) => {
    await deleteShoppingItemMutation(id);
  };

  const saveToPantry = async (items: PantryItem[]) => {
    console.log('💾 AppContext: saveToPantry called with', items.length, 'items');

    // Process items sequentially to ensure proper completion
    for (const item of items) {
      const { id, addedDate, addedById, ...itemData } = item;
      console.log('💾 Saving item to pantry:', itemData.name);

      try {
        // Use the async version to wait for completion
        await addPantryItemAsyncMutation(itemData);
        console.log('✅ Item saved successfully:', itemData.name);
      } catch (error) {
        console.error('❌ Error saving item:', itemData.name, error);
        throw error; // Re-throw to stop the process if any item fails
      }
    }

    console.log('✅ All items saved to pantry successfully');
  };

  const clearCompletedItems = async () => {
    await clearCompletedItemsMutation();
  };

  const updateNotificationSettings = (settings: Partial<NotificationSettings>) => {
    setNotificationSettings(prev => ({
      ...prev,
      ...settings
    }));
  };

  return (
    <AppContext.Provider value={{
      pantryItems,
      shoppingItems,
      activeTab,
      setActiveTab,
      showAddShoppingForm,
      setShowAddShoppingForm,
      showAddPantryForm,
      setShowAddPantryForm,
      addPantryItem,
      updatePantryItem,
      deletePantryItem,
      addShoppingItem,
      addShoppingItems,
      toggleShoppingItem,
      deleteShoppingItem,
      saveToPantry,
      clearCompletedItems,
      notificationSettings,
      updateNotificationSettings,
      isLoadingPantry,
      isLoadingShopping,
    }}>
      {children}
    </AppContext.Provider>
  );
};
