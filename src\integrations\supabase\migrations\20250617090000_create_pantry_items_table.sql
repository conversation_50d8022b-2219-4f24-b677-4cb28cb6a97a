-- Migration para criar a tabela pantry_items
CREATE TABLE IF NOT EXISTS public.pantry_items (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  name text NOT NULL,
  quantity integer NOT NULL,
  unit text NOT NULL,
  category text NOT NULL,
  expiry_date date,
  added_by uuid NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  is_low_stock boolean NOT NULL DEFAULT false,
  
  -- <PERSON><PERSON> estrangeira<PERSON>
  CONSTRAINT pantry_items_pkey PRIMARY KEY (id),
  CONSTRAINT pantry_items_added_by_fkey FOREIGN KEY (added_by) 
    REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Índices
CREATE INDEX IF NOT EXISTS pantry_items_added_by_idx ON public.pantry_items (added_by);
CREATE INDEX IF NOT EXISTS pantry_items_expiry_date_idx ON public.pantry_items (expiry_date);

-- Trigger para updated_at
CREATE OR REPLACE FUNCTION update_pantry_items_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER pantry_items_updated_at_trigger
BEFORE UPDATE ON public.pantry_items
FOR EACH ROW
EXECUTE FUNCTION update_pantry_items_updated_at();

-- RLS (Row Level Security)
ALTER TABLE public.pantry_items ENABLE ROW LEVEL SECURITY;

-- Políticas de segurança
CREATE POLICY "Permitir leitura para usuários autenticados" 
ON public.pantry_items
FOR SELECT
TO authenticated
USING (true);

CREATE POLICY "Permitir inserção para usuários autenticados"
ON public.pantry_items
FOR INSERT
TO authenticated
WITH CHECK (true);

CREATE POLICY "Permitir atualização apenas para donos"
ON public.pantry_items
FOR UPDATE
TO authenticated
USING (added_by = auth.uid());

CREATE POLICY "Permitir exclusão apenas para donos" 
ON public.pantry_items
FOR DELETE
TO authenticated
USING (added_by = auth.uid());