import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { ShoppingCart, Box } from "lucide-react";

interface AddItemSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectList: () => void;
  onSelectPantry: () => void;
}

export const AddItemSelectionModal = ({
  isOpen,
  onClose,
  onSelectList,
  onSelectPantry
}: AddItemSelectionModalProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="z-[9999] w-[calc(100%-2rem)] max-w-sm rounded-2xl shadow-2xl border-0 bg-white sm:w-full"
        aria-describedby="modal-description"
      >
        <p id="modal-description" className="sr-only">
          Selecione uma opção para adicionar itens - lista de compras ou despensa
        </p>
        <DialogHeader className="pb-2">
          <DialogTitle className="text-center text-lg font-semibold text-gray-800">
            Adicionar Item
          </DialogTitle>
        </DialogHeader>
        <div className="grid grid-cols-1 gap-4 px-2 pb-2">
          <Button
            onClick={onSelectList}
            className="flex flex-col items-center justify-center h-24 gap-3 rounded-xl border-2 border-green-200 hover:border-green-300 hover:bg-green-50 transition-all duration-200 shadow-sm"
            variant="outline"
          >
            <ShoppingCart className="w-7 h-7 text-green-600" />
            <span className="text-sm font-medium text-gray-700">Adicionar à Lista de Compras</span>
          </Button>
          <Button
            onClick={onSelectPantry}
            className="flex flex-col items-center justify-center h-24 gap-3 rounded-xl border-2 border-blue-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 shadow-sm"
            variant="outline"
          >
            <Box className="w-7 h-7 text-blue-600" />
            <span className="text-sm font-medium text-gray-700">Adicionar à Despensa</span>
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};