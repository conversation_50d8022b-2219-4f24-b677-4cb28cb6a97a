
// Tipos do banco de dados Supabase
export interface Database {
  public: {
    Tables: {
      shopping_items: {
        Row: {
          id: string;
          name: string;
          quantity: number;
          unit: string;
          category: string;
          checked: boolean;
          user_id: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          quantity: number;
          unit: string;
          category?: string;
          checked?: boolean;
          user_id: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          quantity?: number;
          unit?: string;
          category?: string;
          checked?: boolean;
          user_id?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      pantry_items: {
        Row: {
          id: string;
          name: string;
          quantity: number;
          unit: string;
          category: string;
          expiry_date: string | null;
          is_low_stock: boolean;
          user_id: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          quantity: number;
          unit: string;
          category?: string;
          expiry_date?: string | null;
          is_low_stock?: boolean;
          user_id: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          quantity?: number;
          unit?: string;
          category?: string;
          expiry_date?: string | null;
          is_low_stock?: boolean;
          user_id?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      family_groups: {
        Row: {
          id: string;
          name: string;
          invite_code: string;
          created_by: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          invite_code?: string;
          created_by: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          invite_code?: string;
          created_by?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      family_members: {
        Row: {
          id: string;
          family_group_id: string;
          user_id: string;
          role: 'admin' | 'member';
          joined_at: string;
        };
        Insert: {
          id?: string;
          family_group_id: string;
          user_id: string;
          role?: 'admin' | 'member';
          joined_at?: string;
        };
        Update: {
          id?: string;
          family_group_id?: string;
          user_id?: string;
          role?: 'admin' | 'member';
          joined_at?: string;
        };
      };
      family_invitations: {
        Row: {
          id: string;
          family_group_id: string;
          invited_by: string;
          email: string;
          status: 'pending' | 'accepted' | 'declined';
          created_at: string;
          expires_at: string;
        };
        Insert: {
          id?: string;
          family_group_id: string;
          invited_by: string;
          email: string;
          status?: 'pending' | 'accepted' | 'declined';
          created_at?: string;
          expires_at?: string;
        };
        Update: {
          id?: string;
          family_group_id?: string;
          invited_by?: string;
          email?: string;
          status?: 'pending' | 'accepted' | 'declined';
          created_at?: string;
          expires_at?: string;
        };
      };
      profiles: {
        Row: {
          id: string;
          email: string | null;
          full_name: string | null;
          avatar_url: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email?: string | null;
          full_name?: string | null;
          avatar_url?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string | null;
          full_name?: string | null;
          avatar_url?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
  };
}
