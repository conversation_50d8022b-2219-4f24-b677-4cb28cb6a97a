
import { useState } from "react";
import {
  <PERSON>alog,
  DialogContent,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ShoppingItem } from "@/types";
import { useToast } from "@/hooks/use-toast";
import { X, ShoppingCart, Package, ArrowRight, Check } from "lucide-react";
import { CATEGORIES, getProductsByCategory, Product } from "@/data/products";

interface AdvancedShoppingFormProps {
  isOpen: boolean;
  onClose: () => void;
  onAddItems: (items: Omit<ShoppingItem, 'id' | 'addedBy'>[]) => void;
}

interface SelectedProduct extends Product {
  quantity: number;
  unit: string;
  minStock: number;
}

export const AdvancedShoppingForm = ({ isOpen, onClose, onAddItems }: AdvancedShoppingFormProps) => {
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState<'category' | 'products' | 'review'>('category');
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [selectedProducts, setSelectedProducts] = useState<SelectedProduct[]>([]);
  const [tempSelectedProducts, setTempSelectedProducts] = useState<string[]>([]);
  const [newProductName, setNewProductName] = useState<string>("");

  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category);
    setCurrentStep('products');
    setTempSelectedProducts([]);
    setNewProductName("");
  };

  const handleProductToggle = (productId: string) => {
    setTempSelectedProducts(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const handleAddNewProduct = () => {
    if (!newProductName.trim()) {
      toast({
        title: "Erro",
        description: "Digite um nome para o novo produto",
        variant: "destructive"
      });
      return;
    }

    const newProduct: Product = {
      id: `new-${Date.now()}`,
      name: newProductName.trim(),
      category: selectedCategory,
      defaultUnit: "unidades",
      minStockSuggestion: 1,
      maxStockSuggestion: 10,
      alternativeUnits: ["unidades", "kg", "gramas", "litros", "ml"]
    };

    // Adiciona o produto customizado diretamente à lista
    const selectedProduct: SelectedProduct = {
      ...newProduct,
      quantity: newProduct.minStockSuggestion,
      unit: newProduct.defaultUnit,
      minStock: newProduct.minStockSuggestion
    };

    setSelectedProducts(prev => [...prev, selectedProduct]);
    setNewProductName("");

    toast({
      title: "Produto criado!",
      description: `${newProduct.name} foi adicionado à lista.`,
    });
  };

  const handleAddSelectedProducts = (customProducts?: Product[]) => {
    try {
      const products = customProducts || getProductsByCategory(selectedCategory);
      if (!products || products.length === 0) {
        toast({
          title: "Erro",
          description: "Nenhum produto encontrado nesta categoria.",
          variant: "destructive"
        });
        return;
      }

      if (tempSelectedProducts.length === 0) {
        toast({
          title: "Aviso",
          description: "Selecione pelo menos um produto.",
          variant: "default"
        });
        return;
      }

      const newSelectedProducts = tempSelectedProducts.map(productId => {
        const product = products.find(p => p.id === productId);
        if (!product) return null;

        // Verifica se o produto já está na lista
        const existingProduct = selectedProducts.find(p => p.id === productId);

        return {
          ...product,
          quantity: existingProduct ? existingProduct.quantity : product.minStockSuggestion,
          unit: existingProduct ? existingProduct.unit : product.defaultUnit,
          minStock: existingProduct ? existingProduct.minStock : product.minStockSuggestion
        };
      }).filter(Boolean) as SelectedProduct[];

      if (newSelectedProducts.length === 0) {
        toast({
          title: "Aviso",
          description: "Nenhum produto válido selecionado.",
          variant: "default"
        });
        return;
      }

      // Adiciona apenas produtos que não estão na lista
      setSelectedProducts(prev => {
        const updatedProducts = [...prev];
        newSelectedProducts.forEach(newProduct => {
          const existingIndex = updatedProducts.findIndex(p => p.id === newProduct.id);
          if (existingIndex < 0) {
            updatedProducts.push(newProduct);
          }
        });
        return updatedProducts;
      });

      toast({
        title: "Produtos adicionados!",
        description: `${newSelectedProducts.length} ${newSelectedProducts.length === 1 ? 'produto adicionado' : 'produtos adicionados'} à lista.`,
      });

      setCurrentStep('category');
      setSelectedCategory("");
      setTempSelectedProducts([]);
    } catch (error) {
      console.error("Erro ao adicionar produtos selecionados");
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao processar os produtos selecionados.",
        variant: "destructive"
      });
      setCurrentStep('category');
      setSelectedCategory("");
      setTempSelectedProducts([]);
    }
  };

  const handleProductUpdate = (productId: string, field: keyof SelectedProduct, value: any) => {
    setSelectedProducts(prev => 
      prev.map(product => 
        product.id === productId 
          ? { ...product, [field]: value }
          : product
      )
    );
  };

  const handleRemoveProduct = (productId: string) => {
    setSelectedProducts(prev => prev.filter(product => product.id !== productId));
  };

  const handleFinish = () => {
    try {
      if (selectedProducts.length === 0) {
        toast({
          title: "Erro",
          description: "Selecione pelo menos um produto.",
          variant: "destructive"
        });
        return;
      }

      // Validar estoque mínimo e quantidade
      const validationErrors = [];
      const invalidStock = selectedProducts.filter(p => !p.minStock || p.minStock <= 0);
      const invalidQuantity = selectedProducts.filter(p => !p.quantity || p.quantity <= 0);
      
      if (invalidStock.length > 0) {
        validationErrors.push(`Estoque mínimo inválido para: ${invalidStock.map(p => p.name).join(', ')}`);
      }
      if (invalidQuantity.length > 0) {
        validationErrors.push(`Quantidade inválida para: ${invalidQuantity.map(p => p.name).join(', ')}`);
      }
      
      if (validationErrors.length > 0) {
        toast({
          title: "Erro de validação",
          description: validationErrors.join('\n'),
          variant: "destructive",
          duration: 5000
        });
        return;
      }

      const shoppingItems = selectedProducts.map(product => ({
        name: product.name,
        quantity: product.quantity,
        unit: product.unit,
        category: product.category,
        completed: false
      }));

      onAddItems(shoppingItems);

      // Reset form
      setSelectedProducts([]);
      setCurrentStep('category');
      setSelectedCategory("");
      setTempSelectedProducts([]);

      // Don't show toast here - let the parent handle it to avoid duplicates
      // toast({
      //   title: "Sucesso!",
      //   description: `${shoppingItems.length} itens adicionados à lista de compras.`,
      // });

      // Fechar o modal após adicionar os itens
      onClose();

    } catch (error) {
      console.error("Erro ao finalizar lista de compras");
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao adicionar os itens.",
        variant: "destructive"
      });
    }
  };

  const handleBack = () => {
    if (currentStep === 'products') {
      setCurrentStep('category');
      setSelectedCategory("");
      setTempSelectedProducts([]);
    } else if (currentStep === 'review') {
      setCurrentStep('category');
    }
  };

  // Todos os hooks devem vir antes de qualquer retorno condicional
  const availableProducts = selectedCategory ? getProductsByCategory(selectedCategory) : [];

  // Fallback para categoria sem produtos - resetar para categoria com feedback
  if (currentStep === 'products' && availableProducts.length === 0) {
    toast({
      title: "Aviso",
      description: `Nenhum produto encontrado na categoria ${selectedCategory}`,
      variant: "default"
    });
    setCurrentStep('category');
    setSelectedCategory("");
    setTempSelectedProducts([]);
  }

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="max-w-4xl max-h-[90vh] p-0 overflow-hidden rounded-2xl sm:rounded-2xl w-[calc(100vw-2rem)] sm:w-full mx-auto"
        style={{
          margin: '1.5rem auto',
          position: 'fixed',
          left: '50%',
          top: '50%',
          transform: 'translate(-50%, -50%)',
          right: 'auto',
          bottom: 'auto'
        }}
      >
        <div className="bg-white rounded-2xl w-full h-full flex flex-col relative max-h-[90vh]">
        {/* Header */}
        <div className="flex items-center gap-3 p-6 border-b bg-gradient-to-r from-green-50 to-emerald-50">
          <ShoppingCart className="w-6 h-6 text-green-600" />
          <div>
            <h2 className="text-xl font-bold text-gray-800">Lista de Compras Inteligente</h2>
            <p className="text-sm text-gray-600">
              {currentStep === 'category' && 'Selecione uma categoria'}
              {currentStep === 'products' && `Produtos de ${selectedCategory}`}
              {currentStep === 'review' && 'Revisar seleção'}
            </p>
          </div>
        </div>

        {/* Progress Indicator */}
        <div className="flex items-center justify-center p-4 bg-gray-50 border-b">
          <div className="flex items-center gap-2">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              currentStep === 'category' ? 'bg-green-600 text-white' : 'bg-green-100 text-green-600'
            }`}>
              1
            </div>
            <ArrowRight className="w-4 h-4 text-gray-400" />
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              currentStep === 'products' ? 'bg-green-600 text-white' : 
              selectedProducts.length > 0 ? 'bg-green-100 text-green-600' : 'bg-gray-200 text-gray-400'
            }`}>
              2
            </div>
            <ArrowRight className="w-4 h-4 text-gray-400" />
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              currentStep === 'review' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-400'
            }`}>
              <Check className="w-4 h-4" />
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto flex-1 min-h-0">
          {/* Step 1: Category Selection */}
          {currentStep === 'category' && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-2 sm:gap-3">
                {CATEGORIES.map((category) => (
                  <Button
                    key={category}
                    variant="outline"
                    onClick={() => handleCategorySelect(category)}
                    className="h-16 sm:h-20 flex flex-col items-center justify-center gap-1 sm:gap-2 hover:bg-green-50 hover:border-green-300 text-xs sm:text-sm"
                  >
                    <Package className="w-4 h-4 sm:w-5 sm:h-5 text-green-600" />
                    <span className="font-medium text-center leading-tight">{category}</span>
                  </Button>
                ))}
              </div>

              {/* Selected Products Summary */}
              {selectedProducts.length > 0 && (
                <Card className="mt-6">
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <ShoppingCart className="w-5 h-5" />
                      Produtos Selecionados ({selectedProducts.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-2 max-h-40 overflow-y-auto">
                      {selectedProducts.map((product) => (
                        <div key={product.id} className="flex items-center justify-between p-2 border rounded">
                          <div>
                            <p className="font-medium">{product.name}</p>
                            <p className="text-xs text-gray-600">
                              Estoque mínimo: {product.minStock} {product.unit}
                            </p>
                          </div>
                          <button
                            onClick={() => handleRemoveProduct(product.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                    <Button 
                      onClick={() => setCurrentStep('review')}
                      className="w-full mt-4 bg-green-600 hover:bg-green-700"
                    >
                      Revisar e Finalizar
                    </Button>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {/* Step 2: Product Selection */}
          {currentStep === 'products' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Produtos de {selectedCategory}</h3>
                <Badge variant="outline">{tempSelectedProducts.length} selecionados</Badge>
              </div>

              {/* Lista de produtos com checkboxes */}
              <div className="grid gap-2 max-h-64 overflow-y-auto">
                {availableProducts.map((product) => (
                  <div
                    key={product.id}
                    className={`flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                      tempSelectedProducts.includes(product.id)
                        ? 'bg-green-50 border-green-300'
                        : 'hover:bg-gray-50'
                    }`}
                    onClick={() => handleProductToggle(product.id)}
                  >
                    <Checkbox
                      checked={tempSelectedProducts.includes(product.id)}
                      onChange={() => handleProductToggle(product.id)}
                    />
                    <div className="flex-1">
                      <p className="font-medium">{product.name}</p>
                      <p className="text-sm text-gray-600">
                        Unidade padrão: {product.defaultUnit} |
                        Sugestão: {product.minStockSuggestion}-{product.maxStockSuggestion}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Adicionar produto personalizado */}
              <div className="border-t pt-4">
                <Label className="text-sm font-medium">Adicionar produto personalizado</Label>
                <div className="flex gap-2 mt-2">
                  <Input
                    value={newProductName}
                    onChange={(e) => setNewProductName(e.target.value)}
                    placeholder="Nome do novo produto"
                    className="flex-1"
                  />
                  <Button
                    onClick={handleAddNewProduct}
                    disabled={!newProductName.trim()}
                    variant="outline"
                  >
                    Adicionar
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Review and Configure */}
          {currentStep === 'review' && (
            <div className="space-y-4" aria-describedby="review-description">
              <p id="review-description" className="sr-only">
                Revisão e configuração dos produtos selecionados
              </p>
              <h3 className="text-lg font-semibold">Revisar e Configurar Produtos</h3>

              <div className="space-y-4 max-h-96 overflow-y-auto">
                {selectedProducts.map((product) => (
                  <Card key={product.id} className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                      <div>
                        <h4 className="font-medium">{product.name}</h4>
                        <Badge variant="outline" className="text-xs">{product.category}</Badge>
                      </div>

                      <div className="space-y-1">
                        <Label className="text-xs">Quantidade</Label>
                        <Input
                          type="number"
                          min="1"
                          value={product.quantity}
                          onChange={(e) => handleProductUpdate(product.id, 'quantity', parseInt(e.target.value) || 1)}
                          className="h-8"
                        />
                      </div>

                      <div className="space-y-1">
                        <Label className="text-xs">Unidade</Label>
                        <Select
                          value={product.unit}
                          onValueChange={(value) => handleProductUpdate(product.id, 'unit', value)}
                        >
                          <SelectTrigger className="h-8">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value={product.defaultUnit}>{product.defaultUnit}</SelectItem>
                            {product.alternativeUnits.map((unit) => (
                              <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex items-center gap-2">
                        <div className="space-y-1 flex-1">
                          <Label className="text-xs">Estoque Mín.</Label>
                          <Input
                            type="number"
                            min="1"
                            value={product.minStock}
                            onChange={(e) => handleProductUpdate(product.id, 'minStock', parseInt(e.target.value) || 1)}
                            className="h-8"
                          />
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveProduct(product.id)}
                          className="text-red-600 hover:text-red-700 h-8 w-8 p-0 mt-4"
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>

              {selectedProducts.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <ShoppingCart className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                  <p>Nenhum produto selecionado</p>
                  <Button
                    variant="outline"
                    onClick={() => setCurrentStep('category')}
                    className="mt-2"
                  >
                    Adicionar Produtos
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-4 sm:p-6 border-t bg-gray-50 flex-shrink-0">
          <Button
            variant="outline"
            onClick={currentStep === 'category' ? onClose : handleBack}
          >
            {currentStep === 'category' ? 'Cancelar' : 'Voltar'}
          </Button>

          {currentStep === 'products' && (
            <Button
              onClick={() => handleAddSelectedProducts()}
              disabled={tempSelectedProducts.length === 0}
              className="bg-green-600 hover:bg-green-700"
            >
              Adicionar {tempSelectedProducts.length} {tempSelectedProducts.length === 1 ? 'Produto' : 'Produtos'}
            </Button>
          )}

          {currentStep === 'review' && (
            <Button
              onClick={handleFinish}
              className="bg-green-600 hover:bg-green-700"
            >
              Finalizar Lista
            </Button>
          )}
        </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
