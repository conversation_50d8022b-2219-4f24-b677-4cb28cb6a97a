import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { Badge } from '@/components/ui/badge';

export const DebugSupabase = () => {
  const { user } = useAuth();
  const [results, setResults] = useState<any>({});
  const [loading, setLoading] = useState<string | null>(null);

  const runTest = async (testName: string, testFn: () => Promise<any>) => {
    setLoading(testName);
    try {
      const result = await testFn();
      setResults(prev => ({ ...prev, [testName]: { success: true, data: result } }));
    } catch (error) {
      setResults(prev => ({ ...prev, [testName]: { success: false, error: error.message } }));
    } finally {
      setLoading(null);
    }
  };

  const testAuth = () => runTest('auth', async () => {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) throw error;
    return { userId: user?.id, email: user?.email };
  });

  const testPantryItems = () => runTest('pantryItems', async () => {
    const { data, error } = await supabase
      .from('pantry_items')
      .select('*')
      .limit(5);
    if (error) throw error;
    return { count: data?.length, items: data };
  });

  const testShoppingItems = () => runTest('shoppingItems', async () => {
    const { data, error } = await supabase
      .from('shopping_items')
      .select('*')
      .limit(5);
    if (error) throw error;
    return { count: data?.length, items: data };
  });

  const testProfiles = () => runTest('profiles', async () => {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user?.id || '');
    if (error) throw error;
    return { count: data?.length, profile: data?.[0] };
  });

  const testFamilyGroups = () => runTest('familyGroups', async () => {
    const { data, error } = await supabase
      .from('family_groups')
      .select('*');
    if (error) throw error;
    return { count: data?.length, groups: data };
  });

  const testFamilyMembers = () => runTest('familyMembers', async () => {
    const { data, error } = await supabase
      .from('family_members')
      .select('*');
    if (error) throw error;
    return { count: data?.length, members: data };
  });

  const testDirectQuery = () => runTest('directQuery', async () => {
    // Test a simple query without RLS
    const { data, error } = await supabase
      .from('pantry_items')
      .select('id, name, user_id')
      .limit(1);
    if (error) throw error;
    return data;
  });

  const runAllTests = async () => {
    await testAuth();
    await testProfiles();
    await testPantryItems();
    await testShoppingItems();
    await testFamilyGroups();
    await testFamilyMembers();
    await testDirectQuery();
  };

  const renderResult = (testName: string) => {
    const result = results[testName];
    if (!result) return null;

    return (
      <div className="mt-2 p-2 bg-gray-50 rounded">
        <div className="flex items-center gap-2 mb-2">
          <Badge variant={result.success ? "default" : "destructive"}>
            {result.success ? "✓" : "✗"}
          </Badge>
          <span className="font-medium">{testName}</span>
        </div>
        <pre className="text-xs overflow-auto max-h-32">
          {JSON.stringify(result.success ? result.data : result.error, null, 2)}
        </pre>
      </div>
    );
  };

  if (!user) {
    return (
      <Card className="m-4">
        <CardHeader>
          <CardTitle>Debug Supabase</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Usuário não autenticado. Faça login para testar as consultas.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="m-4">
      <CardHeader>
        <CardTitle>Debug Supabase Connection</CardTitle>
        <p className="text-sm text-gray-600">
          Usuário logado: {user.email} (ID: {user.id})
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-wrap gap-2">
          <Button onClick={testAuth} disabled={loading === 'auth'} size="sm">
            {loading === 'auth' ? 'Testando...' : 'Test Auth'}
          </Button>
          <Button onClick={testProfiles} disabled={loading === 'profiles'} size="sm">
            {loading === 'profiles' ? 'Testando...' : 'Test Profiles'}
          </Button>
          <Button onClick={testPantryItems} disabled={loading === 'pantryItems'} size="sm">
            {loading === 'pantryItems' ? 'Testando...' : 'Test Pantry'}
          </Button>
          <Button onClick={testShoppingItems} disabled={loading === 'shoppingItems'} size="sm">
            {loading === 'shoppingItems' ? 'Testando...' : 'Test Shopping'}
          </Button>
          <Button onClick={testFamilyGroups} disabled={loading === 'familyGroups'} size="sm">
            {loading === 'familyGroups' ? 'Testando...' : 'Test Family Groups'}
          </Button>
          <Button onClick={testFamilyMembers} disabled={loading === 'familyMembers'} size="sm">
            {loading === 'familyMembers' ? 'Testando...' : 'Test Family Members'}
          </Button>
          <Button onClick={testDirectQuery} disabled={loading === 'directQuery'} size="sm">
            {loading === 'directQuery' ? 'Testando...' : 'Test Direct Query'}
          </Button>
          <Button onClick={runAllTests} disabled={loading !== null} variant="outline">
            Run All Tests
          </Button>
        </div>

        <div className="space-y-2">
          {renderResult('auth')}
          {renderResult('profiles')}
          {renderResult('pantryItems')}
          {renderResult('shoppingItems')}
          {renderResult('familyGroups')}
          {renderResult('familyMembers')}
          {renderResult('directQuery')}
        </div>
      </CardContent>
    </Card>
  );
};
