import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { Badge } from '@/components/ui/badge';

export const DebugSupabase = () => {
  const { user } = useAuth();
  const [results, setResults] = useState<any>({});
  const [loading, setLoading] = useState<string | null>(null);
  const [inviteCode, setInviteCode] = useState('');

  const runTest = async (testName: string, testFn: () => Promise<any>) => {
    setLoading(testName);
    try {
      const result = await testFn();
      setResults(prev => ({ ...prev, [testName]: { success: true, data: result } }));
    } catch (error) {
      setResults(prev => ({ ...prev, [testName]: { success: false, error: error.message } }));
    } finally {
      setLoading(null);
    }
  };

  const testAuth = () => runTest('auth', async () => {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) throw error;
    return { userId: user?.id, email: user?.email };
  });

  const testPantryItems = () => runTest('pantryItems', async () => {
    const { data, error } = await supabase
      .from('pantry_items')
      .select('*')
      .limit(5);
    if (error) throw error;
    return { count: data?.length, items: data };
  });

  const testShoppingItems = () => runTest('shoppingItems', async () => {
    const { data, error } = await supabase
      .from('shopping_items')
      .select('*')
      .limit(5);
    if (error) throw error;
    return { count: data?.length, items: data };
  });

  const testProfiles = () => runTest('profiles', async () => {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user?.id || '');
    if (error) throw error;
    return { count: data?.length, profile: data?.[0] };
  });

  const testFamilyGroups = () => runTest('familyGroups', async () => {
    const { data: memberships } = await supabase
      .from('family_members')
      .select('family_group_id')
      .eq('user_id', user?.id);

    if (!memberships || memberships.length === 0) {
      return { count: 0, groups: [] };
    }

    const groupIds = memberships.map(m => m.family_group_id);
    const { data: groups } = await supabase
      .from('family_groups')
      .select('*')
      .in('id', groupIds);

    return { count: groups?.length || 0, groups: groups || [] };
  });

  const testFamilyMembers = () => runTest('familyMembers', async () => {
    const { data: memberships } = await supabase
      .from('family_members')
      .select('family_group_id')
      .eq('user_id', user?.id);

    if (!memberships || memberships.length === 0) {
      return { count: 0, members: [] };
    }

    const groupIds = memberships.map(m => m.family_group_id);
    const { data: members } = await supabase
      .from('family_members')
      .select('*')
      .in('family_group_id', groupIds);

    return { count: members?.length || 0, members: members || [] };
  });

  const testDirectQuery = () => runTest('directQuery', async () => {
    return { warning: "Direct queries disabled in production" };
  });

  const testInviteCodeSearch = () => runTest('inviteCodeSearch', async () => {
    if (!inviteCode.trim()) {
      throw new Error('Digite um código de convite para testar');
    }

    const normalizedCode = inviteCode.toUpperCase().replace(/[^A-Z0-9]/g, '');

    const { data: group, error } = await supabase
      .from('family_groups')
      .select('*')
      .eq('invite_code', normalizedCode)
      .single();

    if (error) {
      throw new Error(`Erro: ${error.code} - ${error.message}`);
    }

    return { group, normalizedCode };
  });

  const testFamilySharing = () => runTest('familySharing', async () => {
    // Get current user info
    const { data: { user: currentUser } } = await supabase.auth.getUser();

    // Test family membership (without profiles join since it's not directly related)
    const { data: memberships, error: memberError } = await supabase
      .from('family_members')
      .select(`
        *,
        family_groups(name, created_by)
      `)
      .eq('user_id', currentUser?.id);

    if (memberError) {
      throw new Error(`Erro memberships: ${memberError.message}`);
    }

    // Get profiles separately for family members
    let familyProfiles = [];
    if (memberships && memberships.length > 0) {
      const groupIds = memberships.map(m => m.family_group_id);

      // Get all members of user's family groups
      const { data: allMembers } = await supabase
        .from('family_members')
        .select('user_id')
        .in('family_group_id', groupIds);

      if (allMembers) {
        const userIds = allMembers.map(m => m.user_id);
        const { data: profiles } = await supabase
          .from('profiles')
          .select('id, email, full_name')
          .in('id', userIds);
        familyProfiles = profiles || [];
      }
    }

    // Test if user can see family pantry items (without join)
    const { data: pantryItems, error: pantryError } = await supabase
      .from('pantry_items')
      .select('*')
      .limit(10);

    if (pantryError) {
      throw new Error(`Erro pantry: ${pantryError.message}`);
    }

    // Test if user can see family shopping items (without join)
    const { data: shoppingItems, error: shoppingError } = await supabase
      .from('shopping_items')
      .select('*')
      .limit(10);

    if (shoppingError) {
      throw new Error(`Erro shopping: ${shoppingError.message}`);
    }

    return {
      currentUserEmail: currentUser?.email,
      currentUserId: currentUser?.id,
      memberships: memberships || [],
      familyProfiles: familyProfiles || [],
      pantryCount: pantryItems?.length || 0,
      shoppingCount: shoppingItems?.length || 0,
      pantryItems: pantryItems?.slice(0, 3) || [],
      shoppingItems: shoppingItems?.slice(0, 3) || [],
      pantryOwners: pantryItems?.map(item => item.user_id) || [],
      shoppingOwners: shoppingItems?.map(item => item.user_id) || []
    };
  });

  const runAllTests = async () => {
    await testAuth();
    await testProfiles();
    await testPantryItems();
    await testShoppingItems();
    await testFamilyGroups();
    await testFamilyMembers();
    await testDirectQuery();
    await testFamilySharing();
  };

  const renderResult = (testName: string) => {
    const result = results[testName];
    if (!result) return null;

    return (
      <div className="mt-2 p-2 bg-gray-50 rounded">
        <div className="flex items-center gap-2 mb-2">
          <Badge variant={result.success ? "default" : "destructive"}>
            {result.success ? "✓" : "✗"}
          </Badge>
          <span className="font-medium">{testName}</span>
        </div>
        <pre className="text-xs overflow-auto max-h-32">
          {JSON.stringify(result.success ? result.data : result.error, null, 2)}
        </pre>
      </div>
    );
  };

  if (!user) {
    return (
      <Card className="m-4">
        <CardHeader>
          <CardTitle>Debug Supabase</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Usuário não autenticado. Faça login para testar as consultas.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="m-4">
      <CardHeader>
        <CardTitle>Debug Supabase Connection</CardTitle>
        <p className="text-sm text-gray-600">
          Usuário logado: {user.email} (ID: {user.id})
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex gap-2 items-center">
            <Input
              placeholder="Digite um código de convite (ex: ABC123)"
              value={inviteCode}
              onChange={(e) => setInviteCode(e.target.value)}
              className="max-w-xs"
            />
            <Button
              onClick={testInviteCodeSearch}
              disabled={loading === 'inviteCodeSearch' || !inviteCode.trim()}
              size="sm"
              variant="outline"
            >
              {loading === 'inviteCodeSearch' ? 'Testando...' : 'Test Invite Code'}
            </Button>
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button onClick={testAuth} disabled={loading === 'auth'} size="sm">
            {loading === 'auth' ? 'Testando...' : 'Test Auth'}
          </Button>
          <Button onClick={testProfiles} disabled={loading === 'profiles'} size="sm">
            {loading === 'profiles' ? 'Testando...' : 'Test Profiles'}
          </Button>
          <Button onClick={testPantryItems} disabled={loading === 'pantryItems'} size="sm">
            {loading === 'pantryItems' ? 'Testando...' : 'Test Pantry'}
          </Button>
          <Button onClick={testShoppingItems} disabled={loading === 'shoppingItems'} size="sm">
            {loading === 'shoppingItems' ? 'Testando...' : 'Test Shopping'}
          </Button>
          <Button onClick={testFamilyGroups} disabled={loading === 'familyGroups'} size="sm">
            {loading === 'familyGroups' ? 'Testando...' : 'Test Family Groups'}
          </Button>
          <Button onClick={testFamilyMembers} disabled={loading === 'familyMembers'} size="sm">
            {loading === 'familyMembers' ? 'Testando...' : 'Test Family Members'}
          </Button>
          <Button onClick={testDirectQuery} disabled={loading === 'directQuery'} size="sm">
            {loading === 'directQuery' ? 'Testando...' : 'Test Direct Query'}
          </Button>
          <Button onClick={testFamilySharing} disabled={loading === 'familySharing'} size="sm" variant="secondary">
            {loading === 'familySharing' ? 'Testando...' : 'Test Family Sharing'}
          </Button>
          <Button onClick={runAllTests} disabled={loading !== null} variant="outline">
            Run All Tests
          </Button>
        </div>

        <div className="space-y-2">
          {renderResult('inviteCodeSearch')}
          {renderResult('auth')}
          {renderResult('profiles')}
          {renderResult('pantryItems')}
          {renderResult('shoppingItems')}
          {renderResult('familyGroups')}
          {renderResult('familyMembers')}
          {renderResult('directQuery')}
          {renderResult('familySharing')}
        </div>
      </CardContent>
    </Card>
  );
};
