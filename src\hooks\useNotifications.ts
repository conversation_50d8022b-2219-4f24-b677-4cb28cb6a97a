
import { useState, useEffect } from "react";
import { useAppContext } from "@/contexts/AppContext";

export interface Notification {
  id: string;
  type: 'expiring' | 'lowStock' | 'shoppingList' | 'general';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  itemId?: string;
}

export const useNotifications = () => {
  const { pantryItems, notificationSettings } = useAppContext();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  
  const [hasPermission, setHasPermission] = useState(false);

  useEffect(() => {
    // Check if notifications are supported and permission status
    if ('Notification' in window) {
      setHasPermission(Notification.permission === 'granted');
    }
  }, []);

  // Generate notifications based on pantry items and settings
  useEffect(() => {
    const generateNotifications = () => {
      const newNotifications: Notification[] = [];
      const now = new Date();

      pantryItems.forEach(item => {
        // Check for expiring items
        if (notificationSettings.expiry && item.expiryDate) {
          const expiryDate = new Date(item.expiryDate);
          const daysUntilExpiry = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

          if (daysUntilExpiry <= 3 && daysUntilExpiry >= 0) {
            const existingNotification = notifications.find(n =>
              n.type === 'expiring' && n.itemId === item.id
            );

            if (!existingNotification) {
              newNotifications.push({
                id: `expiring-${item.id}-${Date.now()}`,
                type: 'expiring',
                title: 'Produto vencendo',
                message: `${item.name} vence em ${daysUntilExpiry} ${daysUntilExpiry === 1 ? 'dia' : 'dias'}`,
                timestamp: new Date().toISOString(),
                read: false,
                itemId: item.id
              });
            }
          }
        }

        // Check for low stock items
        if (notificationSettings.stock && item.isLowStock) {
          const existingNotification = notifications.find(n =>
            n.type === 'lowStock' && n.itemId === item.id
          );

          if (!existingNotification) {
            newNotifications.push({
              id: `lowStock-${item.id}-${Date.now()}`,
              type: 'lowStock',
              title: 'Estoque baixo',
              message: `${item.name} está com estoque baixo (${item.quantity} ${item.unit})`,
              timestamp: new Date().toISOString(),
              read: false,
              itemId: item.id
            });
          }
        }
      });

      if (newNotifications.length > 0) {
        setNotifications(prev => [...newNotifications, ...prev]);
      }
    };

    generateNotifications();
  }, [pantryItems, notificationSettings, notifications]);

  const requestPermission = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      setHasPermission(permission === 'granted');
      return permission === 'granted';
    }
    return false;
  };

  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      read: false
    };
    
    setNotifications(prev => [newNotification, ...prev]);
    
    // Send web push notification if permission granted
    if (hasPermission) {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        badge: '/favicon.ico'
      });
    }
  };

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === id ? { ...notif, read: true } : notif
      )
    );
  };

  const clearAll = () => {
    setNotifications([]);
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return {
    notifications,
    unreadCount,
    hasPermission,
    addNotification,
    markAsRead,
    clearAll,
    requestPermission
  };
};
