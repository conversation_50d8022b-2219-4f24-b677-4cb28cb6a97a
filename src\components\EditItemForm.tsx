
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { PantryItem } from "@/types";
import { useToast } from "@/hooks/use-toast";

interface EditItemFormProps {
  item: PantryItem;
  onUpdateItem: (id: string, updates: Partial<PantryItem>) => void;
  onClose: () => void;
}

const categories = [
  "Laticínios",
  "Carnes", 
  "Verduras",
  "Frutas",
  "Grãos",
  "Enlatados",
  "Bebidas",
  "Temperos",
  "Congelados",
  "Limpeza",
  "Outros"
];

const units = [
  "unidades",
  "kg", 
  "gramas",
  "litros",
  "ml",
  "pacotes",
  "caixas",
  "latas"
];

export const EditItemForm = ({ item, onUpdateItem, onClose }: EditItemFormProps) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: item.name,
    quantity: item.quantity.toString(),
    unit: item.unit,
    category: item.category,
    expiryDate: item.expiryDate,
    isLowStock: item.isLowStock
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.quantity || !formData.unit || !formData.category || !formData.expiryDate) {
      toast({
        title: "Erro",
        description: "Por favor, preencha todos os campos obrigatórios.",
        variant: "destructive"
      });
      return;
    }

    onUpdateItem(item.id, {
      name: formData.name,
      quantity: parseInt(formData.quantity),
      unit: formData.unit,
      category: formData.category,
      expiryDate: formData.expiryDate,
      isLowStock: formData.isLowStock
    });

    toast({
      title: "Sucesso!",
      description: "Item atualizado com sucesso.",
    });

    onClose();
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Nome do Item *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            placeholder="Ex: Leite, Arroz, Tomate..."
            className="bg-white"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="category">Categoria *</Label>
          <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
            <SelectTrigger className="bg-white">
              <SelectValue placeholder="Selecione a categoria" />
            </SelectTrigger>
            <SelectContent className="bg-white">
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="quantity">Quantidade *</Label>
          <Input
            id="quantity"
            type="number"
            value={formData.quantity}
            onChange={(e) => setFormData(prev => ({ ...prev, quantity: e.target.value }))}
            placeholder="Ex: 2"
            min="0.1"
            step="0.1"
            className="bg-white"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="unit">Unidade *</Label>
          <Select value={formData.unit} onValueChange={(value) => setFormData(prev => ({ ...prev, unit: value }))}>
            <SelectTrigger className="bg-white">
              <SelectValue placeholder="Selecione a unidade" />
            </SelectTrigger>
            <SelectContent className="bg-white">
              {units.map((unit) => (
                <SelectItem key={unit} value={unit}>
                  {unit}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="expiryDate">Data de Validade *</Label>
          <Input
            id="expiryDate"
            type="date"
            value={formData.expiryDate}
            onChange={(e) => setFormData(prev => ({ ...prev, expiryDate: e.target.value }))}
            className="bg-white"
          />
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="isLowStock"
            checked={formData.isLowStock}
            onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isLowStock: checked }))}
          />
          <Label htmlFor="isLowStock">Marcar como estoque baixo</Label>
        </div>
      </div>

      <div className="flex gap-2">
        <Button type="submit" className="flex-1 bg-green-600 hover:bg-green-700 text-white">
          Salvar Alterações
        </Button>
        <Button type="button" variant="outline" onClick={onClose} className="flex-1">
          Cancelar
        </Button>
      </div>
    </form>
  );
};
