# PantryPal 🛒

Uma aplicação inteligente de lista de compras colaborativa e gestão de despensa, construída com React, TypeScript e Supabase.

![PantryPal](https://i.postimg.cc/mrFXt52R/logo-despensa-removebg.png)

## ✨ Funcionalidades

- 📦 **Gestão de Despensa**: Controle seus itens com datas de validade e alertas de estoque baixo
- 🛒 **Lista de Compras Inteligente**: Crie listas baseadas em produtos pré-cadastrados
- 👥 **Colaborativo**: Compartilhe listas com família e amigos
- 📱 **Responsivo**: Interface otimizada para mobile e desktop
- 🔄 **Sincronização em Tempo Real**: Atualizações instantâneas entre dispositivos
- 🎯 **PWA**: Instale como aplicativo no seu dispositivo

## 🚀 Tecnologias

- **Frontend**: React 18, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Real-time)
- **Estado**: React Query (TanStack Query)
- **UI**: Shadcn/ui, Lucide Icons
- **Build**: Vite

## Project info

**URL**: https://lovable.dev/projects/bc848757-1972-48a9-a1bf-5ef81833f65e

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/bc848757-1972-48a9-a1bf-5ef81833f65e) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/bc848757-1972-48a9-a1bf-5ef81833f65e) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
