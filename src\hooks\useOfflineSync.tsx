
import { useState, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useToast } from './use-toast';

export const useOfflineSync = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [pendingActions, setPendingActions] = useState<any[]>([]);
  const queryClient = useQueryClient();
  const { toast } = useToast();

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      toast({
        title: "Conexão restaurada",
        description: "Sincronizando dados...",
      });
      
      // Retry pending actions
      if (pendingActions.length > 0) {
        console.log('Retrying pending actions:', pendingActions);
        // Here you would implement retry logic for failed mutations
        setPendingActions([]);
      }
      
      // Refresh all queries
      queryClient.invalidateQueries();
    };

    const handleOffline = () => {
      setIsOnline(false);
      toast({
        title: "Sem conexão",
        description: "Algumas funcionalidades podem estar limitadas.",
        variant: "destructive",
      });
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [pendingActions, queryClient, toast]);

  const addPendingAction = (action: any) => {
    setPendingActions(prev => [...prev, action]);
  };

  return {
    isOnline,
    pendingActions,
    addPendingAction,
  };
};
