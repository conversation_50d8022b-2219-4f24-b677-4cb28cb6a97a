
import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { useToast } from './use-toast';

export const useRealtime = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!user) return;

    console.log('Setting up realtime subscriptions...');

    // Subscribe to pantry items changes
    const pantryChannel = supabase
      .channel('pantry-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'pantry_items'
        },
        (payload) => {
          console.log('Pantry change received:', payload);
          queryClient.invalidateQueries({ queryKey: ['pantryItems'] });
          
          if (payload.eventType === 'INSERT' && payload.new?.user_id !== user.id) {
            toast({
              title: "Item adicionado à despensa",
              description: `Novo item: ${payload.new?.name}`,
            });
          }
        }
      )
      .subscribe();

    // Subscribe to shopping items changes
    const shoppingChannel = supabase
      .channel('shopping-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'shopping_items'
        },
        (payload) => {
          console.log('Shopping change received:', payload);
          queryClient.invalidateQueries({ queryKey: ['shoppingItems'] });
          
          if (payload.eventType === 'UPDATE' && payload.new?.checked && payload.new?.user_id !== user.id) {
            toast({
              title: "Item comprado",
              description: `Item marcado como comprado: ${payload.new?.name}`,
            });
          }
        }
      )
      .subscribe();

    // Subscribe to family group changes
    const familyChannel = supabase
      .channel('family-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'family_members'
        },
        (payload) => {
          console.log('Family change received:', payload);
          queryClient.invalidateQueries({ queryKey: ['familyGroups'] });
          queryClient.invalidateQueries({ queryKey: ['familyMembers'] });
          
          if (payload.eventType === 'INSERT' && payload.new?.user_id !== user.id) {
            toast({
              title: "Novo membro",
              description: "Alguém entrou no grupo familiar",
            });
          }
        }
      )
      .subscribe();

    return () => {
      console.log('Cleaning up realtime subscriptions...');
      supabase.removeChannel(pantryChannel);
      supabase.removeChannel(shoppingChannel);
      supabase.removeChannel(familyChannel);
    };
  }, [user, queryClient, toast]);

  return null;
};
