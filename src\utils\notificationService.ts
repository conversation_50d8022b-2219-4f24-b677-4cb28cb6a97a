import { PantryItem, ShoppingItem } from "@/types";

export class NotificationService {
  private static instance: NotificationService;
  private callbacks: ((notification: any) => void)[] = [];

  static getInstance() {
    if (!this.instance) {
      this.instance = new NotificationService();
    }
    return this.instance;
  }

  subscribe(callback: (notification: any) => void) {
    this.callbacks.push(callback);
    return () => {
      this.callbacks = this.callbacks.filter(cb => cb !== callback);
    };
  }

  private notify(notification: any) {
    this.callbacks.forEach(callback => callback(notification));
  }

  checkExpiringItems(items: PantryItem[]) {
    const today = new Date();
    
    items.forEach(item => {
      const expiryDate = new Date(item.expiryDate);
      const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
      
      if (daysUntilExpiry <= 3 && daysUntilExpiry > 0) {
        this.notify({
          type: 'expiring',
          title: 'Produto vencendo',
          message: `${item.name} vence em ${daysUntilExpiry} dias`,
          itemId: item.id
        });
      } else if (daysUntilExpiry <= 0) {
        this.notify({
          type: 'expiring',
          title: 'Produto vencido',
          message: `${item.name} venceu há ${Math.abs(daysUntilExpiry)} dias`,
          itemId: item.id
        });
      }
    });
  }

  checkLowStock(items: PantryItem[]) {
    items.forEach(item => {
      if (item.isLowStock) {
        this.notify({
          type: 'lowStock',
          title: 'Estoque baixo',
          message: `${item.name} está com estoque baixo (${item.quantity} ${item.unit})`,
          itemId: item.id
        });
      }
    });
  }

  notifyShoppingListAddition(item: ShoppingItem, addedBy: string) {
    this.notify({
      type: 'shoppingList',
      title: 'Item adicionado à lista',
      message: `${addedBy} adicionou ${item.name} à lista de compras`,
      itemId: item.id
    });
  }

  notifyFamilyMember(message: string, memberId: string) {
    this.notify({
      type: 'general',
      title: 'Notificação da família',
      message,
      memberId
    });
  }
}

export const notificationService = NotificationService.getInstance();
