
-- Drop all existing policies for all tables to avoid conflicts
DROP POLICY IF EXISTS "Users can view their family groups" ON public.family_groups;
DROP POLICY IF EXISTS "Users can create family groups" ON public.family_groups;
DROP POLICY IF EXISTS "Admins can update family groups" ON public.family_groups;
DROP POLICY IF EXISTS "Ad<PERSON> can delete family groups" ON public.family_groups;
DROP POLICY IF EXISTS "Family members can view their groups" ON public.family_groups;
DROP POLICY IF EXISTS "Family members can view their groups" ON public.family_groups;

DROP POLICY IF EXISTS "Family members can view other members" ON public.family_members;
DROP POLICY IF EXISTS "Ad<PERSON> can manage family members" ON public.family_members;
DROP POLICY IF EXISTS "Users can leave family groups" ON public.family_members;
DROP POLICY IF EXISTS "Family members can view family members" ON public.family_members;
DROP POLICY IF EXISTS "Family admins can manage members" ON public.family_members;
DROP POLICY IF EXISTS "Users can join families" ON public.family_members;

DROP POLICY IF EXISTS "Users can view invitations for their groups" ON public.family_invitations;
DROP POLICY IF EXISTS "Ad<PERSON> can create invitations" ON public.family_invitations;
DROP POLICY IF EXISTS "Admins can update invitations" ON public.family_invitations;
DROP POLICY IF EXISTS "Family members can view invitations" ON public.family_invitations;
DROP POLICY IF EXISTS "Family admins can manage invitations" ON public.family_invitations;

-- Create RLS policies for pantry_items (family sharing)
CREATE POLICY "Family members can view pantry items" ON public.pantry_items
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.family_members fm
    WHERE fm.user_id = auth.uid()
    AND fm.family_group_id IN (
      SELECT family_group_id FROM public.family_members
      WHERE user_id = pantry_items.user_id
    )
  )
);

CREATE POLICY "Family members can create pantry items" ON public.pantry_items
FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Family members can update pantry items" ON public.pantry_items
FOR UPDATE USING (
  EXISTS (
    SELECT 1 FROM public.family_members fm
    WHERE fm.user_id = auth.uid()
    AND fm.family_group_id IN (
      SELECT family_group_id FROM public.family_members
      WHERE user_id = pantry_items.user_id
    )
  )
);

CREATE POLICY "Family members can delete pantry items" ON public.pantry_items
FOR DELETE USING (
  EXISTS (
    SELECT 1 FROM public.family_members fm
    WHERE fm.user_id = auth.uid()
    AND fm.family_group_id IN (
      SELECT family_group_id FROM public.family_members
      WHERE user_id = pantry_items.user_id
    )
  )
);

-- Create RLS policies for shopping_items (family sharing)
CREATE POLICY "Family members can view shopping items" ON public.shopping_items
FOR SELECT USING (
  user_id = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM public.family_members fm1
    JOIN public.family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid() AND fm2.user_id = shopping_items.user_id
  )
);

CREATE POLICY "Family members can create shopping items" ON public.shopping_items
FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Family members can update shopping items" ON public.shopping_items
FOR UPDATE USING (
  user_id = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM public.family_members fm1
    JOIN public.family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid() AND fm2.user_id = shopping_items.user_id
  )
);

CREATE POLICY "Family members can delete shopping items" ON public.shopping_items
FOR DELETE USING (
  user_id = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM public.family_members fm1
    JOIN public.family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid() AND fm2.user_id = shopping_items.user_id
  )
);

-- Create RLS policies for family_groups
CREATE POLICY "Users can view their family groups" ON public.family_groups
FOR SELECT USING (
  created_by = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM public.family_members 
    WHERE family_group_id = family_groups.id AND user_id = auth.uid()
  )
);

CREATE POLICY "Users can create family groups" ON public.family_groups
FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Admins can update family groups" ON public.family_groups
FOR UPDATE USING (
  EXISTS (
    SELECT 1 FROM public.family_members 
    WHERE family_group_id = family_groups.id 
    AND user_id = auth.uid() 
    AND role = 'admin'
  )
);

-- Create RLS policies for family_members  
CREATE POLICY "Family members can view other members" ON public.family_members
FOR SELECT USING (
  user_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM public.family_groups fg
    WHERE fg.id = family_members.family_group_id
    AND (
      fg.created_by = auth.uid() OR
      EXISTS (
        SELECT 1 FROM public.family_members fm
        WHERE fm.family_group_id = fg.id
        AND fm.user_id = auth.uid()
      )
    )
  )
);

CREATE POLICY "Users can join families" ON public.family_members
FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Admins can manage family members" ON public.family_members
FOR UPDATE USING (
  EXISTS (
    SELECT 1 FROM public.family_members fm 
    WHERE fm.family_group_id = family_members.family_group_id 
    AND fm.user_id = auth.uid() 
    AND fm.role = 'admin'
  )
);

CREATE POLICY "Users can leave family groups" ON public.family_members
FOR DELETE USING (user_id = auth.uid());

-- Create RLS policies for family_invitations
CREATE POLICY "Users can view invitations for their groups" ON public.family_invitations
FOR SELECT USING (
  invited_by = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM public.family_members 
    WHERE family_group_id = family_invitations.family_group_id 
    AND user_id = auth.uid()
  )
);

CREATE POLICY "Admins can create invitations" ON public.family_invitations
FOR INSERT WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.family_members 
    WHERE family_group_id = family_invitations.family_group_id 
    AND user_id = auth.uid() 
    AND role = 'admin'
  )
);

CREATE POLICY "Admins can update invitations" ON public.family_invitations
FOR UPDATE USING (
  EXISTS (
    SELECT 1 FROM public.family_members 
    WHERE family_group_id = family_invitations.family_group_id 
    AND user_id = auth.uid() 
    AND role = 'admin'
  )
);

-- Enable realtime for all tables
ALTER TABLE public.pantry_items REPLICA IDENTITY FULL;
ALTER TABLE public.shopping_items REPLICA IDENTITY FULL;
ALTER TABLE public.family_groups REPLICA IDENTITY FULL;
ALTER TABLE public.family_members REPLICA IDENTITY FULL;
ALTER TABLE public.family_invitations REPLICA IDENTITY FULL;

-- Add tables to realtime publication
ALTER PUBLICATION supabase_realtime ADD TABLE public.pantry_items;
ALTER PUBLICATION supabase_realtime ADD TABLE public.shopping_items;
ALTER PUBLICATION supabase_realtime ADD TABLE public.family_groups;
ALTER PUBLICATION supabase_realtime ADD TABLE public.family_members;
ALTER PUBLICATION supabase_realtime ADD TABLE public.family_invitations;
