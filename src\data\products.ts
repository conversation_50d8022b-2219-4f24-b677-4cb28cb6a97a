// Base de dados de produtos por categoria
export interface Product {
  id: string;
  name: string;
  category: string;
  defaultUnit: string;
  alternativeUnits: string[];
  minStockSuggestion: number;
  maxStockSuggestion: number;
}

export const PRODUCTS_DATABASE: Record<string, Product[]> = {
  "Laticínios": [
    { id: "lat_001", name: "Leite Integral", category: "Laticínios", defaultUnit: "litros", alternativeUnits: ["ml", "caixas"], minStockSuggestion: 2, maxStockSuggestion: 5 },
    { id: "lat_002", name: "Le<PERSON> Desnatado", category: "Laticínios", defaultUnit: "litros", alternativeUnits: ["ml", "caixas"], minStockSuggestion: 1, maxStockSuggestion: 3 },
    { id: "lat_003", name: "Iogurte Natural", category: "Laticínios", defaultUnit: "unidades", alternativeUnits: ["potes", "ml"], minStockSuggestion: 4, maxStockSuggestion: 8 },
    { id: "lat_004", name: "<PERSON><PERSON><PERSON>", category: "Laticínios", defaultUnit: "kg", alternativeUnits: ["gramas", "fatias"], minStockSuggestion: 1, maxStockSuggestion: 2 },
    { id: "lat_005", name: "Queijo Prato", category: "Laticínios", defaultUnit: "kg", alternativeUnits: ["gramas", "fatias"], minStockSuggestion: 1, maxStockSuggestion: 2 },
    { id: "lat_006", name: "Requeijão", category: "Laticínios", defaultUnit: "unidades", alternativeUnits: ["potes", "gramas"], minStockSuggestion: 1, maxStockSuggestion: 3 },
    { id: "lat_007", name: "Manteiga", category: "Laticínios", defaultUnit: "unidades", alternativeUnits: ["gramas", "potes"], minStockSuggestion: 1, maxStockSuggestion: 2 },
    { id: "lat_008", name: "Ovos", category: "Laticínios", defaultUnit: "unidades", alternativeUnits: ["dúzias", "cartelas"], minStockSuggestion: 12, maxStockSuggestion: 24 },
  ],
  
  "Carnes": [
    { id: "car_001", name: "Frango Inteiro", category: "Carnes", defaultUnit: "kg", alternativeUnits: ["gramas", "unidades"], minStockSuggestion: 1, maxStockSuggestion: 3 },
    { id: "car_002", name: "Peito de Frango", category: "Carnes", defaultUnit: "kg", alternativeUnits: ["gramas"], minStockSuggestion: 1, maxStockSuggestion: 2 },
    { id: "car_003", name: "Carne Bovina (Alcatra)", category: "Carnes", defaultUnit: "kg", alternativeUnits: ["gramas"], minStockSuggestion: 1, maxStockSuggestion: 2 },
    { id: "car_004", name: "Carne Moída", category: "Carnes", defaultUnit: "kg", alternativeUnits: ["gramas"], minStockSuggestion: 1, maxStockSuggestion: 2 },
    { id: "car_005", name: "Linguiça", category: "Carnes", defaultUnit: "kg", alternativeUnits: ["gramas", "unidades"], minStockSuggestion: 1, maxStockSuggestion: 2 },
    { id: "car_006", name: "Peixe (Tilápia)", category: "Carnes", defaultUnit: "kg", alternativeUnits: ["gramas", "unidades"], minStockSuggestion: 1, maxStockSuggestion: 2 },
  ],

  "Verduras": [
    { id: "ver_001", name: "Tomate", category: "Verduras", defaultUnit: "kg", alternativeUnits: ["gramas", "unidades"], minStockSuggestion: 1, maxStockSuggestion: 2 },
    { id: "ver_002", name: "Cebola", category: "Verduras", defaultUnit: "kg", alternativeUnits: ["gramas", "unidades"], minStockSuggestion: 1, maxStockSuggestion: 2 },
    { id: "ver_003", name: "Alho", category: "Verduras", defaultUnit: "unidades", alternativeUnits: ["gramas", "cabeças"], minStockSuggestion: 2, maxStockSuggestion: 5 },
    { id: "ver_004", name: "Batata", category: "Verduras", defaultUnit: "kg", alternativeUnits: ["gramas", "unidades"], minStockSuggestion: 2, maxStockSuggestion: 5 },
    { id: "ver_005", name: "Cenoura", category: "Verduras", defaultUnit: "kg", alternativeUnits: ["gramas", "unidades"], minStockSuggestion: 1, maxStockSuggestion: 2 },
    { id: "ver_006", name: "Alface", category: "Verduras", defaultUnit: "unidades", alternativeUnits: ["maços"], minStockSuggestion: 1, maxStockSuggestion: 3 },
    { id: "ver_007", name: "Brócolis", category: "Verduras", defaultUnit: "unidades", alternativeUnits: ["kg", "gramas"], minStockSuggestion: 1, maxStockSuggestion: 2 },
  ],

  "Frutas": [
    { id: "fru_001", name: "Banana", category: "Frutas", defaultUnit: "kg", alternativeUnits: ["gramas", "unidades", "cachos"], minStockSuggestion: 1, maxStockSuggestion: 3 },
    { id: "fru_002", name: "Maçã", category: "Frutas", defaultUnit: "kg", alternativeUnits: ["gramas", "unidades"], minStockSuggestion: 1, maxStockSuggestion: 2 },
    { id: "fru_003", name: "Laranja", category: "Frutas", defaultUnit: "kg", alternativeUnits: ["gramas", "unidades"], minStockSuggestion: 1, maxStockSuggestion: 3 },
    { id: "fru_004", name: "Limão", category: "Frutas", defaultUnit: "unidades", alternativeUnits: ["kg", "gramas"], minStockSuggestion: 3, maxStockSuggestion: 10 },
    { id: "fru_005", name: "Abacaxi", category: "Frutas", defaultUnit: "unidades", alternativeUnits: ["kg"], minStockSuggestion: 1, maxStockSuggestion: 2 },
    { id: "fru_006", name: "Uva", category: "Frutas", defaultUnit: "kg", alternativeUnits: ["gramas", "cachos"], minStockSuggestion: 1, maxStockSuggestion: 2 },
  ],

  "Grãos": [
    { id: "gra_001", name: "Arroz Branco", category: "Grãos", defaultUnit: "kg", alternativeUnits: ["gramas", "pacotes"], minStockSuggestion: 2, maxStockSuggestion: 10 },
    { id: "gra_002", name: "Feijão Carioca", category: "Grãos", defaultUnit: "kg", alternativeUnits: ["gramas", "pacotes"], minStockSuggestion: 1, maxStockSuggestion: 5 },
    { id: "gra_003", name: "Feijão Preto", category: "Grãos", defaultUnit: "kg", alternativeUnits: ["gramas", "pacotes"], minStockSuggestion: 1, maxStockSuggestion: 3 },
    { id: "gra_004", name: "Lentilha", category: "Grãos", defaultUnit: "kg", alternativeUnits: ["gramas", "pacotes"], minStockSuggestion: 1, maxStockSuggestion: 2 },
    { id: "gra_005", name: "Grão de Bico", category: "Grãos", defaultUnit: "kg", alternativeUnits: ["gramas", "pacotes"], minStockSuggestion: 1, maxStockSuggestion: 2 },
    { id: "gra_006", name: "Macarrão Espaguete", category: "Grãos", defaultUnit: "pacotes", alternativeUnits: ["kg", "gramas"], minStockSuggestion: 2, maxStockSuggestion: 5 },
    { id: "gra_007", name: "Macarrão Penne", category: "Grãos", defaultUnit: "pacotes", alternativeUnits: ["kg", "gramas"], minStockSuggestion: 1, maxStockSuggestion: 3 },
  ],

  "Enlatados": [
    { id: "enl_001", name: "Molho de Tomate", category: "Enlatados", defaultUnit: "latas", alternativeUnits: ["unidades", "ml"], minStockSuggestion: 2, maxStockSuggestion: 6 },
    { id: "enl_002", name: "Atum em Lata", category: "Enlatados", defaultUnit: "latas", alternativeUnits: ["unidades"], minStockSuggestion: 2, maxStockSuggestion: 5 },
    { id: "enl_003", name: "Sardinha em Lata", category: "Enlatados", defaultUnit: "latas", alternativeUnits: ["unidades"], minStockSuggestion: 2, maxStockSuggestion: 4 },
    { id: "enl_004", name: "Milho em Conserva", category: "Enlatados", defaultUnit: "latas", alternativeUnits: ["unidades"], minStockSuggestion: 1, maxStockSuggestion: 3 },
    { id: "enl_005", name: "Ervilha em Conserva", category: "Enlatados", defaultUnit: "latas", alternativeUnits: ["unidades"], minStockSuggestion: 1, maxStockSuggestion: 3 },
  ],

  "Bebidas": [
    { id: "beb_001", name: "Água Mineral", category: "Bebidas", defaultUnit: "litros", alternativeUnits: ["ml", "garrafas"], minStockSuggestion: 5, maxStockSuggestion: 20 },
    { id: "beb_002", name: "Refrigerante Cola", category: "Bebidas", defaultUnit: "litros", alternativeUnits: ["ml", "garrafas", "latas"], minStockSuggestion: 1, maxStockSuggestion: 3 },
    { id: "beb_003", name: "Suco de Laranja", category: "Bebidas", defaultUnit: "litros", alternativeUnits: ["ml", "caixas"], minStockSuggestion: 1, maxStockSuggestion: 3 },
    { id: "beb_004", name: "Café em Pó", category: "Bebidas", defaultUnit: "pacotes", alternativeUnits: ["kg", "gramas"], minStockSuggestion: 1, maxStockSuggestion: 3 },
    { id: "beb_005", name: "Chá (Sachês)", category: "Bebidas", defaultUnit: "caixas", alternativeUnits: ["unidades", "sachês"], minStockSuggestion: 1, maxStockSuggestion: 2 },
  ],

  "Temperos": [
    { id: "tem_001", name: "Sal", category: "Temperos", defaultUnit: "kg", alternativeUnits: ["gramas", "pacotes"], minStockSuggestion: 1, maxStockSuggestion: 2 },
    { id: "tem_002", name: "Açúcar", category: "Temperos", defaultUnit: "kg", alternativeUnits: ["gramas", "pacotes"], minStockSuggestion: 1, maxStockSuggestion: 3 },
    { id: "tem_003", name: "Óleo de Soja", category: "Temperos", defaultUnit: "litros", alternativeUnits: ["ml", "garrafas"], minStockSuggestion: 1, maxStockSuggestion: 2 },
    { id: "tem_004", name: "Vinagre", category: "Temperos", defaultUnit: "ml", alternativeUnits: ["litros", "garrafas"], minStockSuggestion: 1, maxStockSuggestion: 2 },
    { id: "tem_005", name: "Pimenta do Reino", category: "Temperos", defaultUnit: "gramas", alternativeUnits: ["unidades", "potes"], minStockSuggestion: 1, maxStockSuggestion: 2 },
    { id: "tem_006", name: "Orégano", category: "Temperos", defaultUnit: "gramas", alternativeUnits: ["unidades", "potes"], minStockSuggestion: 1, maxStockSuggestion: 2 },
  ],

  "Congelados": [
    { id: "con_001", name: "Batata Frita Congelada", category: "Congelados", defaultUnit: "kg", alternativeUnits: ["gramas", "pacotes"], minStockSuggestion: 1, maxStockSuggestion: 3 },
    { id: "con_002", name: "Hambúrguer Congelado", category: "Congelados", defaultUnit: "unidades", alternativeUnits: ["pacotes"], minStockSuggestion: 4, maxStockSuggestion: 12 },
    { id: "con_003", name: "Pizza Congelada", category: "Congelados", defaultUnit: "unidades", alternativeUnits: ["pacotes"], minStockSuggestion: 1, maxStockSuggestion: 3 },
    { id: "con_004", name: "Sorvete", category: "Congelados", defaultUnit: "litros", alternativeUnits: ["ml", "potes"], minStockSuggestion: 1, maxStockSuggestion: 2 },
  ],

  "Limpeza": [
    { id: "lim_001", name: "Detergente", category: "Limpeza", defaultUnit: "unidades", alternativeUnits: ["ml", "frascos"], minStockSuggestion: 2, maxStockSuggestion: 5 },
    { id: "lim_002", name: "Sabão em Pó", category: "Limpeza", defaultUnit: "kg", alternativeUnits: ["gramas", "caixas"], minStockSuggestion: 1, maxStockSuggestion: 3 },
    { id: "lim_003", name: "Papel Higiênico", category: "Limpeza", defaultUnit: "unidades", alternativeUnits: ["pacotes", "rolos"], minStockSuggestion: 4, maxStockSuggestion: 12 },
    { id: "lim_004", name: "Papel Toalha", category: "Limpeza", defaultUnit: "unidades", alternativeUnits: ["pacotes", "rolos"], minStockSuggestion: 2, maxStockSuggestion: 6 },
    { id: "lim_005", name: "Desinfetante", category: "Limpeza", defaultUnit: "litros", alternativeUnits: ["ml", "frascos"], minStockSuggestion: 1, maxStockSuggestion: 3 },
  ],

  "Outros": [
    { id: "out_001", name: "Pilhas AA", category: "Outros", defaultUnit: "unidades", alternativeUnits: ["pacotes"], minStockSuggestion: 4, maxStockSuggestion: 8 },
    { id: "out_002", name: "Pilhas AAA", category: "Outros", defaultUnit: "unidades", alternativeUnits: ["pacotes"], minStockSuggestion: 4, maxStockSuggestion: 8 },
    { id: "out_003", name: "Lâmpada LED", category: "Outros", defaultUnit: "unidades", alternativeUnits: [], minStockSuggestion: 1, maxStockSuggestion: 3 },
  ]
};

export const CATEGORIES = Object.keys(PRODUCTS_DATABASE);

export const getProductsByCategory = (category: string): Product[] => {
  return PRODUCTS_DATABASE[category] || [];
};

export const getAllProducts = (): Product[] => {
  return Object.values(PRODUCTS_DATABASE).flat();
};

export const findProductById = (id: string): Product | undefined => {
  return getAllProducts().find(product => product.id === id);
};
