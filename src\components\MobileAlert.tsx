
import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { AlertTriangle, Calendar, ShoppingCart, Plus, X, ChevronDown, ChevronUp } from "lucide-react";

interface AlertItem {
  id: string;
  type: 'lowStock' | 'expiring' | 'expired';
  title: string;
  message: string;
  priority: 'high' | 'medium' | 'low';
  itemId?: string;
}

interface MobileAlertProps {
  alerts: AlertItem[];
  onQuickAction: (action: string, itemId?: string) => void;
  onDismiss: (alertId: string) => void;
}

export const MobileAlert = ({ alerts, onQuickAction, onDismiss }: MobileAlertProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (alerts.length === 0) return null;

  const priorityAlerts = alerts.sort((a, b) => {
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    return priorityOrder[b.priority] - priorityOrder[a.priority];
  });

  const getIcon = (type: string) => {
    switch (type) {
      case 'lowStock':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'expiring':
      case 'expired':
        return <Calendar className="w-4 h-4 text-orange-500" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getBadgeColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'destructive';
      case 'medium':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  return (
    <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
      <Card className="bg-gradient-to-r from-red-50 to-orange-50 border-red-200 shadow-sm">
        <CollapsibleTrigger asChild>
          <div className="w-full cursor-pointer">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                    <AlertTriangle className="w-5 h-5 text-red-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800 text-base">
                      Alertas Importantes
                    </h3>
                    <p className="text-sm text-gray-600">
                      {alerts.length} {alerts.length === 1 ? 'alerta' : 'alertas'} pendente{alerts.length === 1 ? '' : 's'}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="destructive" className="text-xs">
                    {alerts.length}
                  </Badge>
                  {isExpanded ? (
                    <ChevronUp className="w-5 h-5 text-gray-400" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-gray-400" />
                  )}
                </div>
              </div>
            </CardContent>
          </div>
        </CollapsibleTrigger>
        
        <CollapsibleContent className="animate-accordion-down">
          <div className="px-4 pb-4 space-y-3">
            {priorityAlerts.slice(0, 3).map((alert) => (
              <Card key={alert.id} className="bg-white shadow-sm border-l-4 border-l-red-400">
                <CardContent className="p-3">
                  <div className="flex items-start gap-3">
                    {getIcon(alert.type)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <p className="font-medium text-sm text-gray-800 truncate">
                          {alert.title}
                        </p>
                        <Badge variant={getBadgeColor(alert.priority)} className="text-xs">
                          {alert.priority === 'high' ? 'Urgente' : alert.priority === 'medium' ? 'Médio' : 'Baixo'}
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                        {alert.message}
                      </p>
                      <div className="flex items-center gap-2">
                        {alert.type === 'lowStock' && (
                          <Button
                            size="sm"
                            variant="outline"
                            className="h-7 px-2 text-xs"
                            onClick={() => onQuickAction('addToShoppingList', alert.itemId)}
                          >
                            <Plus className="w-3 h-3 mr-1" />
                            Adicionar
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-7 px-2 text-xs"
                          onClick={() => onQuickAction('viewItem', alert.itemId)}
                        >
                          Ver
                        </Button>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={() => onDismiss(alert.id)}
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
            
            {alerts.length > 3 && (
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => onQuickAction('viewAllAlerts')}
              >
                Ver todos os {alerts.length} alertas
              </Button>
            )}
          </div>
        </CollapsibleContent>
      </Card>
    </Collapsible>
  );
};
