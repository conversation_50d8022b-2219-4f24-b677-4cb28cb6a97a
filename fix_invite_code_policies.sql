-- Execute this SQL directly in Supabase SQL Editor to fix invite code search issues
-- This script fixes the RLS policies to allow searching family groups by invite code

-- Drop existing policies that might conflict
DROP POLICY IF EXISTS "Users can view their family groups" ON public.family_groups;
DROP POLICY IF EXISTS "Allow invite code search" ON public.family_groups;

-- Create policy for users to view their own family groups
CREATE POLICY "Users can view their family groups" ON public.family_groups
FOR SELECT USING (
  created_by = auth.uid() OR
  id IN (
    SELECT family_group_id FROM public.family_members
    WHERE user_id = auth.uid()
  )
);

-- Allow any authenticated user to search family groups by invite code
CREATE POLICY "Allow invite code search" ON public.family_groups
FOR SELECT USING (
  auth.role() = 'authenticated' AND
  invite_code IS NOT NULL
);

-- Also ensure family_members INSERT policy exists for joining groups
DROP POLICY IF EXISTS "Users can join family groups" ON public.family_members;

CREATE POLICY "Users can join family groups" ON public.family_members
FOR INSERT WITH CHECK (
  user_id = auth.uid()
);

-- Fix pantry_items policies to allow family sharing
DROP POLICY IF EXISTS "Family members can view pantry items" ON public.pantry_items;
DROP POLICY IF EXISTS "Family members can update pantry items" ON public.pantry_items;
DROP POLICY IF EXISTS "Family members can delete pantry items" ON public.pantry_items;
DROP POLICY IF EXISTS "pantry_select_own" ON public.pantry_items;
DROP POLICY IF EXISTS "pantry_update_own" ON public.pantry_items;
DROP POLICY IF EXISTS "pantry_delete_own" ON public.pantry_items;

CREATE POLICY "Family members can view pantry items" ON public.pantry_items
FOR SELECT USING (
  user_id = auth.uid() OR
  user_id IN (
    SELECT fm2.user_id FROM public.family_members fm1
    JOIN public.family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

CREATE POLICY "Family members can update pantry items" ON public.pantry_items
FOR UPDATE USING (
  user_id = auth.uid() OR
  user_id IN (
    SELECT fm2.user_id FROM public.family_members fm1
    JOIN public.family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

CREATE POLICY "Family members can delete pantry items" ON public.pantry_items
FOR DELETE USING (
  user_id = auth.uid() OR
  user_id IN (
    SELECT fm2.user_id FROM public.family_members fm1
    JOIN public.family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

-- Fix shopping_items policies to allow family sharing
DROP POLICY IF EXISTS "Family members can view shopping items" ON public.shopping_items;
DROP POLICY IF EXISTS "Family members can update shopping items" ON public.shopping_items;
DROP POLICY IF EXISTS "Family members can delete shopping items" ON public.shopping_items;
DROP POLICY IF EXISTS "shopping_select_own" ON public.shopping_items;
DROP POLICY IF EXISTS "shopping_update_own" ON public.shopping_items;
DROP POLICY IF EXISTS "shopping_delete_own" ON public.shopping_items;

CREATE POLICY "Family members can view shopping items" ON public.shopping_items
FOR SELECT USING (
  user_id = auth.uid() OR
  user_id IN (
    SELECT fm2.user_id FROM public.family_members fm1
    JOIN public.family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

CREATE POLICY "Family members can update shopping items" ON public.shopping_items
FOR UPDATE USING (
  user_id = auth.uid() OR
  user_id IN (
    SELECT fm2.user_id FROM public.family_members fm1
    JOIN public.family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

CREATE POLICY "Family members can delete shopping items" ON public.shopping_items
FOR DELETE USING (
  user_id = auth.uid() OR
  user_id IN (
    SELECT fm2.user_id FROM public.family_members fm1
    JOIN public.family_members fm2 ON fm1.family_group_id = fm2.family_group_id
    WHERE fm1.user_id = auth.uid()
  )
);

-- Allow users to leave family groups
DROP POLICY IF EXISTS "Users can leave family groups" ON public.family_members;

CREATE POLICY "Users can leave family groups" ON public.family_members
FOR DELETE USING (user_id = auth.uid());

-- Test the policies by running these queries after applying the fix:
-- 1. Search for a group by invite code (replace 'ABC123' with actual code):
-- SELECT * FROM family_groups WHERE invite_code = 'ABC123';
--
-- 2. Check if you can see family pantry items:
-- SELECT * FROM pantry_items LIMIT 10;
--
-- 3. Check if you can see family shopping items:
-- SELECT * FROM shopping_items LIMIT 10;
