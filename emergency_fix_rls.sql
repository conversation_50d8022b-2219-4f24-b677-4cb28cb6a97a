-- EMERGENCY FIX: Remove all problematic RLS policies and create simple ones
-- Execute this SQL in Supabase SQL Editor immediately

-- 1. DISABLE RLS temporarily to break the recursion
ALTER TABLE public.family_members DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.family_groups DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.pantry_items DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.shopping_items DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.family_invitations DISABLE ROW LEVEL SECURITY;

-- 2. DROP ALL existing policies to remove recursion
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.profiles;

DROP POLICY IF EXISTS "Users can view their pantry items" ON public.pantry_items;
DROP POLICY IF EXISTS "Users can insert their own pantry items" ON public.pantry_items;
DROP POLICY IF EXISTS "Users can update their own pantry items" ON public.pantry_items;
DROP POLICY IF EXISTS "Users can delete their own pantry items" ON public.pantry_items;
DROP POLICY IF EXISTS "Family members can view pantry items" ON public.pantry_items;
DROP POLICY IF EXISTS "Family members can update pantry items" ON public.pantry_items;
DROP POLICY IF EXISTS "Family members can delete pantry items" ON public.pantry_items;

DROP POLICY IF EXISTS "Users can view their shopping items" ON public.shopping_items;
DROP POLICY IF EXISTS "Users can insert their own shopping items" ON public.shopping_items;
DROP POLICY IF EXISTS "Users can update their own shopping items" ON public.shopping_items;
DROP POLICY IF EXISTS "Users can delete their own shopping items" ON public.shopping_items;
DROP POLICY IF EXISTS "Family members can view shopping items" ON public.shopping_items;
DROP POLICY IF EXISTS "Family members can update shopping items" ON public.shopping_items;
DROP POLICY IF EXISTS "Family members can delete shopping items" ON public.shopping_items;

DROP POLICY IF EXISTS "Users can create family groups" ON public.family_groups;
DROP POLICY IF EXISTS "Users can view their family groups" ON public.family_groups;
DROP POLICY IF EXISTS "Admins can update family groups" ON public.family_groups;

DROP POLICY IF EXISTS "Users can join family groups" ON public.family_members;
DROP POLICY IF EXISTS "Family members can view other members" ON public.family_members;
DROP POLICY IF EXISTS "Admins can manage family members" ON public.family_members;

DROP POLICY IF EXISTS "Users can create invitations" ON public.family_invitations;
DROP POLICY IF EXISTS "Users can view invitations for their groups" ON public.family_invitations;
DROP POLICY IF EXISTS "Admins can create invitations" ON public.family_invitations;
DROP POLICY IF EXISTS "Admins can update invitations" ON public.family_invitations;

-- 3. CREATE SIMPLE, NON-RECURSIVE POLICIES

-- Profiles policies (simple)
CREATE POLICY "profiles_select_own" ON public.profiles
FOR SELECT USING (auth.uid() = id);

CREATE POLICY "profiles_update_own" ON public.profiles
FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "profiles_insert_own" ON public.profiles
FOR INSERT WITH CHECK (auth.uid() = id);

-- Pantry items policies (user-only, no family for now)
CREATE POLICY "pantry_select_own" ON public.pantry_items
FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "pantry_insert_own" ON public.pantry_items
FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "pantry_update_own" ON public.pantry_items
FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "pantry_delete_own" ON public.pantry_items
FOR DELETE USING (auth.uid() = user_id);

-- Shopping items policies (user-only, no family for now)
CREATE POLICY "shopping_select_own" ON public.shopping_items
FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "shopping_insert_own" ON public.shopping_items
FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "shopping_update_own" ON public.shopping_items
FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "shopping_delete_own" ON public.shopping_items
FOR DELETE USING (auth.uid() = user_id);

-- Family groups policies (simple)
CREATE POLICY "family_groups_select_own" ON public.family_groups
FOR SELECT USING (auth.uid() = created_by);

CREATE POLICY "family_groups_insert_own" ON public.family_groups
FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "family_groups_update_own" ON public.family_groups
FOR UPDATE USING (auth.uid() = created_by);

-- Family members policies (simple, no recursion)
CREATE POLICY "family_members_select_own" ON public.family_members
FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "family_members_insert_own" ON public.family_members
FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "family_members_update_own" ON public.family_members
FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "family_members_delete_own" ON public.family_members
FOR DELETE USING (auth.uid() = user_id);

-- Family invitations policies (simple)
CREATE POLICY "family_invitations_select_own" ON public.family_invitations
FOR SELECT USING (auth.uid() = invited_by);

CREATE POLICY "family_invitations_insert_own" ON public.family_invitations
FOR INSERT WITH CHECK (auth.uid() = invited_by);

CREATE POLICY "family_invitations_update_own" ON public.family_invitations
FOR UPDATE USING (auth.uid() = invited_by);

-- 4. RE-ENABLE RLS with simple policies
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.pantry_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.shopping_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.family_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.family_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.family_invitations ENABLE ROW LEVEL SECURITY;

-- 5. Test query to verify it works
SELECT 'RLS policies fixed successfully' as status;
