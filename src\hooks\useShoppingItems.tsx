import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { ShoppingItem } from '@/types';
import { convertDbShoppingItem, convertToShoppingDbFormat } from '@/utils/database';
import { useToast } from './use-toast';

export const useShoppingItems = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: shoppingItems = [], isLoading, error } = useQuery({
    queryKey: ['shoppingItems', user?.id],
    queryFn: async () => {
      if (!user) return [];
      
      // Simplified query without joins to test RLS policies
      const { data, error } = await supabase
        .from('shopping_items')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching shopping items:', error);
        throw error;
      }

      // Configura listener para mudanças em tempo real
      // Remove filter to listen to all changes - R<PERSON> will handle access control
      const channel = supabase
        .channel('shopping_items_changes')
        .on('postgres_changes', {
          event: '*',
          schema: 'public',
          table: 'shopping_items'
          // No filter - let RLS handle what the user can see
        }, (payload) => {
          console.log('🔄 Shopping items realtime update:', payload);
          queryClient.invalidateQueries({ queryKey: ['shoppingItems', user.id] });
        })
        .subscribe();

      // Retorna função de limpeza
      const unsubscribe = () => {
        supabase.removeChannel(channel);
      };

      // Garante cleanup quando componente desmonta
      window.addEventListener('beforeunload', unsubscribe);

      // Return the converted data directly, not wrapped in an object
      return data.map(convertDbShoppingItem);
    },
    enabled: !!user,
    retry: 3,
    staleTime: 0, // Force fresh data always
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    // Remove the select function since we're returning the array directly
  });

  const addItemMutation = useMutation({
    mutationFn: async (item: Omit<ShoppingItem, 'id' | 'addedBy'>) => {
      if (!user) throw new Error('User not authenticated');
      
      // Check for existing item with same name and category
      const existingItems = shoppingItems.filter(
        existingItem =>
          existingItem.name.toLowerCase() === item.name.toLowerCase() &&
          existingItem.category === item.category &&
          !existingItem.completed
      );

      if (existingItems.length > 0) {
        // Update existing item quantity
        const existingItem = existingItems[0];
        const { data, error } = await supabase
          .from('shopping_items')
          .update({ quantity: existingItem.quantity + item.quantity })
          .eq('id', existingItem.id)
          .select()
          .single();

        if (error) throw error;
        return convertDbShoppingItem(data);
      } else {
        // Create new item
        const dbItem = convertToShoppingDbFormat(item, user.id);
        const { data, error } = await supabase
          .from('shopping_items')
          .insert(dbItem)
          .select()
          .single();

        if (error) throw error;
        return convertDbShoppingItem(data);
      }
    },
    onMutate: async (newItem) => {
      await queryClient.cancelQueries({ queryKey: ['shoppingItems', user?.id] });
      const previousItems = queryClient.getQueryData(['shoppingItems', user?.id]);

      const optimisticItem: ShoppingItem = {
        id: 'temp-' + Date.now(),
        ...newItem,
        addedBy: user?.id || '',
      };

      queryClient.setQueryData(['shoppingItems', user?.id], (old: ShoppingItem[] = []) => [
        optimisticItem,
        ...old,
      ]);

      return { previousItems };
    },
    onError: (err, _newItem, context) => {
      queryClient.setQueryData(['shoppingItems', user?.id], context?.previousItems);
      toast({
        title: "Erro",
        description: "Erro ao adicionar item à lista.",
        variant: "destructive",
      });
      console.error('Error adding shopping item:', err);
    },
    onSuccess: () => {
      toast({
        title: "Item adicionado",
        description: "Item adicionado à lista de compras.",
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['shoppingItems', user?.id] });
    },
  });

  const addMultipleItemsMutation = useMutation({
    mutationFn: async (items: Omit<ShoppingItem, 'id' | 'addedBy'>[]) => {
      console.log('🔄 addMultipleItemsMutation started');
      console.log('📦 Items to add:', items);
      console.log('👤 User:', user);

      if (!user) {
        console.error('❌ User not authenticated');
        throw new Error('User not authenticated');
      }

      const operations = [];

      for (const item of items) {
        console.log(`🔍 Processing item: ${item.name}`);

        const existingItems = shoppingItems.filter(
          existingItem =>
            existingItem.name.toLowerCase() === item.name.toLowerCase() &&
            existingItem.category === item.category &&
            !existingItem.completed
        );

        if (existingItems.length > 0) {
          const existingItem = existingItems[0];
          console.log(`📝 Updating existing item: ${existingItem.name}`);
          operations.push(
            supabase
              .from('shopping_items')
              .update({ quantity: existingItem.quantity + item.quantity })
              .eq('id', existingItem.id)
          );
        } else {
          const dbItem = convertToShoppingDbFormat(item, user.id);
          console.log(`➕ Inserting new item:`, dbItem);
          operations.push(
            supabase
              .from('shopping_items')
              .insert(dbItem)
          );
        }
      }

      console.log(`🚀 Executing ${operations.length} operations`);
      const results = await Promise.all(operations);
      console.log('📊 Results:', results);

      const errors = results.filter(result => result.error);
      if (errors.length > 0) {
        console.error('❌ Errors found:', errors);
        throw new Error('Failed to add some items');
      }

      console.log('✅ All operations completed successfully');
    },
    // Removed onMutate to avoid optimistic update conflicts
    onError: (err, newItems) => {
      console.error('❌ addMultipleItemsMutation onError:', err);
      console.error('📦 Failed items:', newItems);
      toast({
        title: "Erro",
        description: `Erro ao adicionar itens: ${err.message}`,
        variant: "destructive",
      });
      console.error('Error adding shopping items:', err);
    },
    onSuccess: () => {
      console.log('✅ addMultipleItemsMutation onSuccess');

      // Force immediate and aggressive cache invalidation
      queryClient.invalidateQueries({ queryKey: ['shoppingItems', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['shoppingItems'] });

      // Force refetch with immediate execution
      queryClient.refetchQueries({
        queryKey: ['shoppingItems', user?.id],
        type: 'active',
        exact: true
      });

      console.log('🔄 Cache invalidated and refetch triggered');

      toast({
        title: "Itens adicionados",
        description: "Itens adicionados à lista de compras.",
      });
    },
    onSettled: () => {
      console.log('🏁 addMultipleItemsMutation onSettled');
      // Double-check invalidation
      queryClient.invalidateQueries({ queryKey: ['shoppingItems', user?.id] });
    },
  });

  const toggleItemMutation = useMutation({
    mutationFn: async (id: string) => {
      const item = shoppingItems.find(item => item.id === id);
      if (!item) throw new Error('Item not found');

      const { data, error } = await supabase
        .from('shopping_items')
        .update({ checked: !item.completed })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return convertDbShoppingItem(data);
    },
    onMutate: async (toggledId) => {
      await queryClient.cancelQueries({ queryKey: ['shoppingItems', user?.id] });
      const previousItems = queryClient.getQueryData(['shoppingItems', user?.id]);

      queryClient.setQueryData(['shoppingItems', user?.id], (old: ShoppingItem[] = []) => 
        old.map(item => item.id === toggledId ? { ...item, completed: !item.completed } : item)
      );

      return { previousItems };
    },
    onError: (err, _variables, context) => {
      queryClient.setQueryData(['shoppingItems', user?.id], context?.previousItems);
      toast({
        title: "Erro",
        description: "Erro ao atualizar item.",
        variant: "destructive",
      });
      console.error('Error toggling shopping item:', err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['shoppingItems', user?.id] });
    },
  });

  const deleteItemMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('shopping_items')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onMutate: async (deletedId) => {
      await queryClient.cancelQueries({ queryKey: ['shoppingItems', user?.id] });
      const previousItems = queryClient.getQueryData(['shoppingItems', user?.id]);

      queryClient.setQueryData(['shoppingItems', user?.id], (old: ShoppingItem[] = []) => 
        old.filter(item => item.id !== deletedId)
      );

      return { previousItems };
    },
    onError: (err, _variables, context) => {
      queryClient.setQueryData(['shoppingItems', user?.id], context?.previousItems);
      toast({
        title: "Erro",
        description: "Erro ao remover item.",
        variant: "destructive",
      });
      console.error('Error deleting shopping item:', err);
    },
    onSuccess: () => {
      toast({
        title: "Item removido",
        description: "Item removido da lista de compras.",
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['shoppingItems', user?.id] });
    },
  });

  const clearCompletedMutation = useMutation({
    mutationFn: async () => {
      const { error } = await supabase
        .from('shopping_items')
        .delete()
        .eq('checked', true);

      if (error) throw error;
    },
    onSuccess: () => {
      // Force immediate refetch after clearing completed items
      queryClient.invalidateQueries({ queryKey: ['shoppingItems', user?.id] });
      queryClient.refetchQueries({ queryKey: ['shoppingItems', user?.id], type: 'active' });

      toast({
        title: "Itens limpos",
        description: "Itens comprados removidos da lista.",
      });
    },
    onError: (error) => {
      toast({
        title: "Erro",
        description: "Erro ao limpar itens comprados.",
        variant: "destructive",
      });
      console.error('Error clearing completed items:', error);
    },
  });

  return {
    shoppingItems,
    isLoading,
    error,
    addItem: addItemMutation.mutate,
    addMultipleItems: addMultipleItemsMutation.mutateAsync,
    toggleItem: toggleItemMutation.mutateAsync,
    deleteItem: deleteItemMutation.mutateAsync,
    clearCompleted: clearCompletedMutation.mutateAsync,
    isAddingItem: addItemMutation.isPending,
    isTogglingItem: toggleItemMutation.isPending,
    isDeletingItem: deleteItemMutation.isPending,
    isAddingMultiple: addMultipleItemsMutation.isPending,
    isClearingCompleted: clearCompletedMutation.isPending,
  };
};
