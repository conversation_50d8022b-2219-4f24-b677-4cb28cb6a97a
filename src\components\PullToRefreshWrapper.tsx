
import React, { useState, useRef, useEffect } from 'react';
import { RefreshCw } from 'lucide-react';

interface PullToRefreshWrapperProps {
  onRefresh: () => Promise<void>;
  isRefreshing: boolean;
  children: React.ReactNode;
}

export const PullToRefreshWrapper = ({ onRefresh, isRefreshing, children }: PullToRefreshWrapperProps) => {
  const [pullDistance, setPullDistance] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [startY, setStartY] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  const threshold = 80;
  const maxPull = 120;

  const handleTouchStart = (e: React.TouchEvent) => {
    if (containerRef.current && containerRef.current.scrollTop === 0) {
      setStartY(e.touches[0].clientY);
      setIsDragging(true);
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging || isRefreshing) return;

    const currentY = e.touches[0].clientY;
    const distance = Math.min(Math.max(currentY - startY, 0), maxPull);
    setPullDistance(distance);

    if (distance > 0) {
      e.preventDefault();
    }
  };

  const handleTouchEnd = async () => {
    if (!isDragging) return;

    setIsDragging(false);

    if (pullDistance >= threshold && !isRefreshing) {
      await onRefresh();
    }

    setPullDistance(0);
  };

  useEffect(() => {
    if (isRefreshing) {
      setPullDistance(0);
    }
  }, [isRefreshing]);

  const refreshIconRotation = isRefreshing ? 'animate-spin' : 
    pullDistance >= threshold ? 'rotate-180' : 
    `rotate-${Math.min(pullDistance * 2, 180)}`;

  return (
    <div 
      ref={containerRef}
      className="h-full overflow-auto relative"
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* Pull to refresh indicator */}
      <div 
        className={`absolute top-0 left-0 right-0 z-10 flex items-center justify-center transition-all duration-300 ${
          pullDistance > 0 || isRefreshing ? 'opacity-100' : 'opacity-0'
        }`}
        style={{ 
          height: Math.max(pullDistance, isRefreshing ? 60 : 0),
          transform: `translateY(${isRefreshing ? 0 : -60}px)`
        }}
      >
        <div className="bg-white rounded-full p-2 shadow-lg">
          <RefreshCw 
            className={`w-6 h-6 text-green-600 transition-transform duration-300 ${refreshIconRotation}`} 
          />
        </div>
      </div>

      {/* Main content */}
      <div 
        className="transition-transform duration-300"
        style={{ 
          transform: `translateY(${pullDistance}px)`,
          paddingTop: isRefreshing ? '60px' : '0px'
        }}
      >
        {children}
      </div>
    </div>
  );
};
