import { useState } from 'react';
import { useSwipeable } from 'react-swipeable';

interface PullToRefreshProps {
  onRefresh: () => Promise<void>;
  children: React.ReactNode;
}

export const PullToRefresh = ({ onRefresh, children }: PullToRefreshProps) => {
  const [refreshing, setRefreshing] = useState(false);
  const [startY, setStartY] = useState(0);
  const [pullDelta, setPullDelta] = useState(0);

  const handlers = useSwipeable({
    onSwiping: (e) => {
      if (window.scrollY === 0 && e.dir === 'Down') {
        const touchY = 'touches' in e.event ? e.event.touches[0].clientY : 0;
        const delta = Math.max(0, touchY - startY);
        setPullDelta(Math.min(delta, 100));
      }
    },
    onTouchStartOrOnMouseDown: (e) => {
      if (window.scrollY === 0) {
        const touchY = 'touches' in e.event ? e.event.touches[0].clientY : 0;
        setStartY(touchY);
      }
    },
    onTouchEndOrOnMouseUp: async () => {
      if (pullDelta > 50) {
        setRefreshing(true);
        await onRefresh();
        setRefreshing(false);
      }
      setPullDelta(0);
    },
    trackTouch: true,
    trackMouse: false
  });

  return (
    <div {...handlers} style={{ position: 'relative' }}>
      {refreshing && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          display: 'flex',
          justifyContent: 'center',
          padding: '10px',
          backgroundColor: '#4CAF50',
          color: 'white',
          zIndex: 1000
        }}>
          Atualizando...
        </div>
      )}
      
      {pullDelta > 0 && !refreshing && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          height: `${pullDelta}px`,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'rgba(76, 175, 80, 0.3)',
          zIndex: 999
        }}>
          <div style={{
            width: '40px',
            height: '40px',
            borderRadius: '50%',
            border: '3px solid #4CAF50',
            borderTopColor: 'transparent',
            animation: 'spin 1s linear infinite',
            transform: `rotate(${pullDelta * 3.6}deg)`
          }} />
        </div>
      )}
      
      {children}
    </div>
  );
};